<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="components/header.js"></script>
    <script src="components/crud.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
            margin: 0 0 5px 0;
        }

        .page-title p {
            color: #7f8c8d;
            margin: 0;
            font-size: 1rem;
        }

        .page-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 15px;
        }

        .stat-card .total .icon { background: linear-gradient(135deg, #667eea, #764ba2); }
        .stat-card .low .icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .stat-card .out .icon { background: linear-gradient(135deg, #95a5a6, #7f8c8d); }
        .stat-card .value .icon { background: linear-gradient(135deg, #27ae60, #229954); }

        .stat-card h3 {
            color: #2c3e50;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .filters-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .inventory-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #2c3e50;
            font-weight: 600;
            position: sticky;
            top: 0;
            cursor: pointer;
        }

        .table th:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #ecf0f1;
        }

        .stock-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .stock-high {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .stock-medium {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
        }

        .stock-low {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .stock-out {
            background: rgba(149, 165, 166, 0.1);
            color: #95a5a6;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 2px;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover {
            background: #f8f9fa;
        }

        .pagination-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .page-actions {
                width: 100%;
                justify-content: center;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-title">
                <h1><i class="fas fa-boxes"></i> إدارة المخزون</h1>
                <p>متابعة وإدارة مخزون المنتجات والكميات</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="adjustStock()">
                    <i class="fas fa-plus-minus"></i> تعديل المخزون
                </button>
                <button class="btn btn-warning" onclick="stockAlert()">
                    <i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون
                </button>
                <button class="btn btn-secondary" onclick="exportInventory()">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="icon"><i class="fas fa-boxes"></i></div>
                <h3>إجمالي المنتجات</h3>
                <div class="number" id="totalProducts">0</div>
            </div>
            
            <div class="stat-card low">
                <div class="icon"><i class="fas fa-exclamation-triangle"></i></div>
                <h3>مخزون منخفض</h3>
                <div class="number" id="lowStockProducts">0</div>
            </div>
            
            <div class="stat-card out">
                <div class="icon"><i class="fas fa-times-circle"></i></div>
                <h3>نفد المخزون</h3>
                <div class="number" id="outOfStockProducts">0</div>
            </div>
            
            <div class="stat-card value">
                <div class="icon"><i class="fas fa-dollar-sign"></i></div>
                <h3>قيمة المخزون</h3>
                <div class="number" id="inventoryValue">0 ر.س</div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filters-grid">
                <div class="form-group">
                    <label>البحث بالاسم</label>
                    <input type="text" class="form-control" id="searchName" placeholder="ادخل اسم المنتج">
                </div>
                <div class="form-group">
                    <label>الفئة</label>
                    <select class="form-control" id="searchCategory">
                        <option value="">جميع الفئات</option>
                        <option value="electronics">إلكترونيات</option>
                        <option value="computers">أجهزة كمبيوتر</option>
                        <option value="smartphones">هواتف ذكية</option>
                        <option value="appliances">أجهزة منزلية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>حالة المخزون</label>
                    <select class="form-control" id="searchStock">
                        <option value="">جميع الحالات</option>
                        <option value="high">مخزون عالي</option>
                        <option value="medium">مخزون متوسط</option>
                        <option value="low">مخزون منخفض</option>
                        <option value="out">نفد المخزون</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="searchInventory()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="inventory-table">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...
            </div>
            
            <div id="inventoryContent" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th onclick="crudManager.sort('id')">الرقم <i class="fas fa-sort"></i></th>
                            <th>الصورة</th>
                            <th onclick="crudManager.sort('productName')">اسم المنتج <i class="fas fa-sort"></i></th>
                            <th onclick="crudManager.sort('category')">الفئة <i class="fas fa-sort"></i></th>
                            <th onclick="crudManager.sort('currentStock')">المخزون الحالي <i class="fas fa-sort"></i></th>
                            <th onclick="crudManager.sort('minStock')">الحد الأدنى <i class="fas fa-sort"></i></th>
                            <th onclick="crudManager.sort('purchasePrice')">سعر الشراء <i class="fas fa-sort"></i></th>
                            <th>قيمة المخزون</th>
                            <th>حالة المخزون</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="inventoryTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <!-- أزرار التصفح -->
                </div>
            </div>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-boxes"></i>
                <h3>لا توجد منتجات في المخزون</h3>
                <p>لم يتم العثور على أي منتجات. ابدأ بإضافة منتجات جديدة.</p>
            </div>
        </div>
    </div>

    <script>
        // Initialize CRUD Manager for inventory
        const crudManager = new CRUDManager({
            apiBaseUrl: '/api',
            itemsPerPage: 15,
            onDisplayData: displayInventory
        });

        // Mock inventory data
        const mockInventory = [
            {
                id: 1,
                productName: 'لابتوب HP Pavilion',
                productCode: 'HP-PAV-001',
                category: 'computers',
                currentStock: 15,
                minStock: 5,
                purchasePrice: 2500,
                salePrice: 3000,
                unit: 'piece',
                imageUrl: null
            },
            {
                id: 2,
                productName: 'هاتف Samsung Galaxy S24',
                productCode: 'SAM-S24-001',
                category: 'smartphones',
                currentStock: 3,
                minStock: 10,
                purchasePrice: 3500,
                salePrice: 4200,
                unit: 'piece',
                imageUrl: null
            },
            {
                id: 3,
                productName: 'طابعة Canon',
                productCode: 'CAN-PRT-001',
                category: 'electronics',
                currentStock: 0,
                minStock: 3,
                purchasePrice: 800,
                salePrice: 1200,
                unit: 'piece',
                imageUrl: null
            }
        ];

        // Load inventory data
        async function loadInventory() {
            await crudManager.read('inventory', {
                mockData: mockInventory,
                onSuccess: (data) => {
                    updateStats(data);
                    console.log('تم تحميل المخزون بنجاح:', data.length);
                },
                onError: (error) => {
                    console.error('خطأ في تحميل المخزون:', error);
                }
            });
        }

        // Display inventory data
        function displayInventory(inventory) {
            const tbody = document.getElementById('inventoryTableBody');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const inventoryContent = document.getElementById('inventoryContent');
            const emptyState = document.getElementById('emptyState');

            if (!inventory || inventory.length === 0) {
                loadingIndicator.style.display = 'none';
                inventoryContent.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tbody.innerHTML = '';
            inventory.forEach(item => {
                const stockStatus = getStockStatus(item.currentStock, item.minStock);
                const inventoryValue = item.currentStock * item.purchasePrice;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.id}</td>
                    <td>
                        <img src="${item.imageUrl || '/images/no-image.png'}" 
                             alt="${item.productName}" 
                             class="product-image"
                             onerror="this.src='/images/no-image.png'">
                    </td>
                    <td>
                        <div style="font-weight: 600;">${item.productName}</div>
                        <div style="font-size: 0.85rem; color: #7f8c8d;">${item.productCode}</div>
                    </td>
                    <td>${getCategoryName(item.category)}</td>
                    <td>
                        <div style="font-weight: 600;">${item.currentStock} ${item.unit || 'قطعة'}</div>
                    </td>
                    <td>${item.minStock} ${item.unit || 'قطعة'}</td>
                    <td>${item.purchasePrice.toLocaleString()} ر.س</td>
                    <td>
                        <div style="font-weight: 600; color: #27ae60;">
                            ${inventoryValue.toLocaleString()} ر.س
                        </div>
                    </td>
                    <td>
                        <span class="stock-badge ${stockStatus.class}">
                            ${stockStatus.text}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-info btn-sm" onclick="viewProduct(${item.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-success btn-sm" onclick="adjustStock(${item.id})" title="تعديل المخزون">
                                <i class="fas fa-plus-minus"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="stockMovements(${item.id})" title="حركات المخزون">
                                <i class="fas fa-history"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            loadingIndicator.style.display = 'none';
            inventoryContent.style.display = 'block';
            emptyState.style.display = 'none';
        }

        // Get stock status
        function getStockStatus(currentStock, minStock) {
            if (currentStock === 0) {
                return { class: 'stock-out', text: 'نفد المخزون' };
            } else if (currentStock <= minStock) {
                return { class: 'stock-low', text: 'مخزون منخفض' };
            } else if (currentStock <= minStock * 2) {
                return { class: 'stock-medium', text: 'مخزون متوسط' };
            } else {
                return { class: 'stock-high', text: 'مخزون عالي' };
            }
        }

        // Get category name
        function getCategoryName(category) {
            const categories = {
                'electronics': 'إلكترونيات',
                'computers': 'أجهزة كمبيوتر',
                'smartphones': 'هواتف ذكية',
                'appliances': 'أجهزة منزلية'
            };
            return categories[category] || category;
        }

        // Update statistics
        function updateStats(inventory) {
            let totalProducts = inventory.length;
            let lowStockProducts = 0;
            let outOfStockProducts = 0;
            let totalValue = 0;

            inventory.forEach(item => {
                if (item.currentStock === 0) {
                    outOfStockProducts++;
                } else if (item.currentStock <= item.minStock) {
                    lowStockProducts++;
                }
                totalValue += item.currentStock * item.purchasePrice;
            });

            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('lowStockProducts').textContent = lowStockProducts;
            document.getElementById('outOfStockProducts').textContent = outOfStockProducts;
            document.getElementById('inventoryValue').textContent = totalValue.toLocaleString() + ' ر.س';
        }

        // Search inventory
        function searchInventory() {
            const filters = {
                productName: document.getElementById('searchName').value,
                category: document.getElementById('searchCategory').value,
                stockStatus: document.getElementById('searchStock').value
            };

            // Remove empty filters
            Object.keys(filters).forEach(key => {
                if (!filters[key]) delete filters[key];
            });

            crudManager.search(filters);
        }

        // View product details
        function viewProduct(id) {
            const product = crudManager.data.find(p => p.id == id);
            if (product) {
                const stockStatus = getStockStatus(product.currentStock, product.minStock);
                const inventoryValue = product.currentStock * product.purchasePrice;
                
                alert(`تفاصيل المنتج:\nالاسم: ${product.productName}\nالكود: ${product.productCode}\nالمخزون الحالي: ${product.currentStock} ${product.unit}\nالحد الأدنى: ${product.minStock} ${product.unit}\nقيمة المخزون: ${inventoryValue.toLocaleString()} ر.س\nالحالة: ${stockStatus.text}`);
            }
        }

        // Adjust stock
        function adjustStock(id = null) {
            if (id) {
                const product = crudManager.data.find(p => p.id == id);
                if (product) {
                    const newStock = prompt(`تعديل مخزون ${product.productName}\nالمخزون الحالي: ${product.currentStock}\nادخل الكمية الجديدة:`, product.currentStock);
                    if (newStock !== null && !isNaN(newStock)) {
                        crudManager.showNotification(`تم تحديث مخزون ${product.productName} إلى ${newStock}`, 'success');
                        // Update local data
                        product.currentStock = parseInt(newStock);
                        crudManager.displayData();
                        updateStats(crudManager.data);
                    }
                }
            } else {
                crudManager.showNotification('يرجى اختيار منتج لتعديل مخزونه', 'warning');
            }
        }

        // Stock movements
        function stockMovements(id) {
            const product = crudManager.data.find(p => p.id == id);
            if (product) {
                alert(`حركات مخزون ${product.productName}\n\nهذه الميزة قيد التطوير وستكون متاحة قريباً.`);
            }
        }

        // Stock alerts
        function stockAlert() {
            const lowStockItems = crudManager.data.filter(item => 
                item.currentStock <= item.minStock && item.currentStock > 0
            );
            const outOfStockItems = crudManager.data.filter(item => 
                item.currentStock === 0
            );

            let alertMessage = 'تنبيهات المخزون:\n\n';
            
            if (outOfStockItems.length > 0) {
                alertMessage += 'منتجات نفد مخزونها:\n';
                outOfStockItems.forEach(item => {
                    alertMessage += `- ${item.productName}\n`;
                });
                alertMessage += '\n';
            }

            if (lowStockItems.length > 0) {
                alertMessage += 'منتجات مخزونها منخفض:\n';
                lowStockItems.forEach(item => {
                    alertMessage += `- ${item.productName} (${item.currentStock}/${item.minStock})\n`;
                });
            }

            if (lowStockItems.length === 0 && outOfStockItems.length === 0) {
                alertMessage += 'جميع المنتجات لديها مخزون كافي! 👍';
            }

            alert(alertMessage);
        }

        // Export inventory
        function exportInventory() {
            crudManager.showNotification('جاري تحضير ملف تصدير المخزون...', 'info');
            setTimeout(() => {
                crudManager.showNotification('تم تصدير بيانات المخزون بنجاح', 'success');
            }, 2000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadInventory();
            
            // Add search event listeners
            document.getElementById('searchName').addEventListener('input', searchInventory);
            document.getElementById('searchCategory').addEventListener('change', searchInventory);
            document.getElementById('searchStock').addEventListener('change', searchInventory);
        });
    </script>
</body>
</html>
