const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// نموذج المنتجات
const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  product_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    field: 'product_code'
  },
  barcode: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true
  },
  name: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      len: [2, 200],
      notEmpty: true
    }
  },
  name_ar: {
    type: DataTypes.STRING(200),
    allowNull: false,
    field: 'name_ar',
    validate: {
      len: [2, 200],
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  category_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'category_id'
  },
  brand_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'brand_id'
  },
  unit_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'unit_id'
  },
  purchase_price: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'purchase_price'
  },
  selling_price: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'selling_price'
  },
  min_selling_price: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'min_selling_price'
  },
  wholesale_price: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'wholesale_price'
  },
  cost_price: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'cost_price'
  },
  weight: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: true
  },
  dimensions: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  color: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  size: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  warranty_period: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'warranty_period'
  },
  reorder_level: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'reorder_level'
  },
  max_stock_level: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'max_stock_level'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  is_serialized: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_serialized'
  },
  track_inventory: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'track_inventory'
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 15,
    field: 'tax_rate'
  },
  image_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'image_url'
  },
  gallery: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'updated_at'
  }
}, {
  tableName: 'products',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['product_code']
    },
    {
      unique: true,
      fields: ['barcode']
    },
    {
      fields: ['category_id']
    },
    {
      fields: ['brand_id']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['created_by']
    },
    {
      type: 'FULLTEXT',
      fields: ['name', 'name_ar', 'description']
    }
  ]
});

// الدوال الإضافية للنموذج
Product.prototype.getDisplayName = function(language = 'ar') {
  return language === 'ar' ? this.name_ar : this.name;
};

Product.prototype.getProfitMargin = function() {
  if (this.cost_price <= 0) return 0;
  return ((this.selling_price - this.cost_price) / this.cost_price) * 100;
};

Product.prototype.getProfitAmount = function() {
  return this.selling_price - this.cost_price;
};

Product.prototype.getWarrantyText = function() {
  if (this.warranty_period <= 0) return 'بدون ضمان';
  if (this.warranty_period < 12) return `${this.warranty_period} شهر`;
  return `${Math.floor(this.warranty_period / 12)} سنة`;
};

Product.prototype.addImage = function(imageUrl) {
  if (!this.gallery) this.gallery = [];
  if (!this.gallery.includes(imageUrl)) {
    this.gallery.push(imageUrl);
  }
};

Product.prototype.removeImage = function(imageUrl) {
  if (!this.gallery) return;
  this.gallery = this.gallery.filter(img => img !== imageUrl);
};

Product.prototype.addSpecification = function(key, value) {
  if (!this.specifications) this.specifications = {};
  this.specifications[key] = value;
};

Product.prototype.removeSpecification = function(key) {
  if (!this.specifications) return;
  delete this.specifications[key];
};

// الدوال الثابتة
Product.generateProductCode = async function(categoryPrefix = 'PRD') {
  const lastProduct = await this.findOne({
    where: {
      product_code: {
        [sequelize.Op.like]: `${categoryPrefix}%`
      }
    },
    order: [['id', 'DESC']],
    attributes: ['product_code']
  });
  
  let nextNumber = 1;
  if (lastProduct && lastProduct.product_code) {
    const lastNumber = parseInt(lastProduct.product_code.replace(categoryPrefix, ''));
    nextNumber = lastNumber + 1;
  }
  
  return `${categoryPrefix}${nextNumber.toString().padStart(6, '0')}`;
};

Product.findByCode = async function(productCode) {
  return await this.findOne({
    where: { product_code: productCode, is_active: true }
  });
};

Product.findByBarcode = async function(barcode) {
  return await this.findOne({
    where: { barcode, is_active: true }
  });
};

Product.findActiveProducts = async function(options = {}) {
  return await this.findAll({
    where: { is_active: true },
    ...options
  });
};

Product.createProduct = async function(productData) {
  try {
    if (!productData.product_code) {
      productData.product_code = await this.generateProductCode();
    }
    
    const product = await this.create(productData);
    return product;
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0].path;
      const fieldName = field === 'product_code' ? 'رمز المنتج' : 'الباركود';
      throw new Error(`${fieldName} مستخدم بالفعل`);
    }
    throw error;
  }
};

Product.updateProduct = async function(id, productData) {
  const product = await this.findByPk(id);
  if (!product) {
    throw new Error('المنتج غير موجود');
  }
  
  await product.update(productData);
  return product;
};

Product.deleteProduct = async function(id) {
  const product = await this.findByPk(id);
  if (!product) {
    throw new Error('المنتج غير موجود');
  }
  
  // التحقق من عدم وجود حركات مخزون
  const InventoryMovement = require('./InventoryMovement');
  const movementsCount = await InventoryMovement.count({ 
    where: { product_id: id }
  });
  
  if (movementsCount > 0) {
    throw new Error('لا يمكن حذف المنتج لوجود حركات مخزون مرتبطة به');
  }
  
  // حذف منطقي
  await product.update({ is_active: false });
  return product;
};

Product.searchProducts = async function(searchTerm, options = {}) {
  const { Op } = require('sequelize');
  
  return await this.findAll({
    where: {
      is_active: true,
      [Op.or]: [
        { product_code: { [Op.like]: `%${searchTerm}%` } },
        { barcode: { [Op.like]: `%${searchTerm}%` } },
        { name: { [Op.like]: `%${searchTerm}%` } },
        { name_ar: { [Op.like]: `%${searchTerm}%` } },
        { description: { [Op.like]: `%${searchTerm}%` } }
      ]
    },
    ...options
  });
};

Product.getProductStats = async function() {
  const totalProducts = await this.count({ where: { is_active: true } });
  const lowStockProducts = await this.count({
    where: {
      is_active: true,
      // سيتم تنفيذ هذا عند إضافة نموذج المخزون
    }
  });
  const outOfStockProducts = await this.count({
    where: {
      is_active: true,
      // سيتم تنفيذ هذا عند إضافة نموذج المخزون
    }
  });
  
  return {
    total: totalProducts,
    lowStock: lowStockProducts,
    outOfStock: outOfStockProducts
  };
};

Product.getProductsByCategory = async function() {
  const results = await this.findAll({
    attributes: [
      'category_id',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    where: { is_active: true },
    group: ['category_id']
  });
  
  return results.map(result => ({
    categoryId: result.category_id,
    count: parseInt(result.dataValues.count)
  }));
};

Product.getTopSellingProducts = async function(limit = 10) {
  // سيتم تنفيذ هذا عند إضافة نموذج فواتير البيع
  return [];
};

Product.getLowStockProducts = async function() {
  // سيتم تنفيذ هذا عند إضافة نموذج المخزون
  return [];
};

// تصدير النموذج
module.exports = Product;
