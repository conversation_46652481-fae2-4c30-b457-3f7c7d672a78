{"name": "commercial-agency-frontend", "version": "1.0.0", "description": "نظام الوكالة التجارية المتكامل - الواجهة الأمامية", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "export": "next export"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@mui/material": "^5.15.1", "@mui/icons-material": "^5.15.1", "@mui/lab": "^5.0.0-alpha.158", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "next-auth": "^4.24.5", "js-cookie": "^3.0.5", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "recharts": "^2.8.0", "react-pdf": "^7.6.0", "jspdf": "^2.5.1", "xlsx": "^0.18.5", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-beautiful-dnd": "^13.1.1", "react-virtualized": "^9.22.5", "lodash": "^4.17.21", "clsx": "^2.0.0", "react-helmet-async": "^2.0.4", "react-intersection-observer": "^9.5.3", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "use-debounce": "^10.0.0", "react-use": "^17.4.2", "react-error-boundary": "^4.0.11", "nprogress": "^0.2.0", "react-loading-skeleton": "^3.3.1"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/nprogress": "^0.2.3", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@next/bundle-analyzer": "^14.0.4", "cross-env": "^7.0.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css,scss}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check"}}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "^@/components/(.*)$": "<rootDir>/src/components/$1", "^@/pages/(.*)$": "<rootDir>/src/pages/$1", "^@/hooks/(.*)$": "<rootDir>/src/hooks/$1", "^@/utils/(.*)$": "<rootDir>/src/utils/$1", "^@/store/(.*)$": "<rootDir>/src/store/$1", "^@/types/(.*)$": "<rootDir>/src/types/$1", "^@/styles/(.*)$": "<rootDir>/src/styles/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/pages/_app.tsx", "!src/pages/_document.tsx"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}}