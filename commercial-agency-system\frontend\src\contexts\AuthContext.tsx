import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  login as loginAction,
  logout as logoutAction,
  checkAuth,
  refreshToken,
  selectAuth,
  selectUser,
  selectIsAuthenticated,
  selectIsLoading,
  selectError,
  clearError,
  User,
} from '@/store/slices/authSlice';
import { authHelpers } from '@/services/api/authAPI';
import LoadingScreen from '@/components/common/LoadingScreen';

// تعريف أنواع البيانات
interface AuthContextType {
  // الحالة
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // الدوال
  login: (credentials: { username: string; password: string; rememberMe?: boolean }) => Promise<void>;
  logout: () => Promise<void>;
  checkAuthentication: () => Promise<void>;
  clearAuthError: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (roles: string[]) => boolean;
  refreshUserToken: () => Promise<void>;
}

// إنشاء Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook لاستخدام AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// خصائص AuthProvider
interface AuthProviderProps {
  children: ReactNode;
}

// مكون AuthProvider
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  
  // الحصول على الحالة من Redux
  const auth = useAppSelector(selectAuth);
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectError);

  // الصفحات التي لا تحتاج مصادقة
  const publicRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password', '/auth/reset-password'];
  
  // الصفحات التي تحتاج مصادقة
  const protectedRoutes = ['/dashboard', '/customers', '/suppliers', '/products', '/inventory', '/sales', '/purchases', '/payments', '/reports', '/settings'];

  // دالة تسجيل الدخول
  const login = async (credentials: { username: string; password: string; rememberMe?: boolean }) => {
    try {
      await dispatch(loginAction(credentials)).unwrap();
      
      // إعادة توجيه بعد تسجيل الدخول الناجح
      const returnUrl = router.query.returnUrl as string || '/dashboard';
      router.push(returnUrl);
    } catch (error) {
      // الخطأ سيتم التعامل معه في Redux
      throw error;
    }
  };

  // دالة تسجيل الخروج
  const logout = async () => {
    try {
      await dispatch(logoutAction()).unwrap();
      
      // مسح التخزين المحلي
      authHelpers.removeToken();
      
      // إعادة توجيه إلى صفحة تسجيل الدخول
      router.push('/auth/login');
    } catch (error) {
      // حتى لو فشل طلب تسجيل الخروج، نقوم بمسح البيانات المحلية
      authHelpers.removeToken();
      router.push('/auth/login');
    }
  };

  // دالة التحقق من المصادقة
  const checkAuthentication = async () => {
    const token = authHelpers.getToken();
    
    if (!token) {
      return;
    }

    // التحقق من انتهاء صلاحية الرمز المميز
    if (authHelpers.isTokenExpired(token)) {
      try {
        await dispatch(refreshToken()).unwrap();
      } catch (error) {
        authHelpers.removeToken();
        return;
      }
    }

    // التحقق من حالة المصادقة
    try {
      await dispatch(checkAuth()).unwrap();
    } catch (error) {
      authHelpers.removeToken();
    }
  };

  // دالة مسح خطأ المصادقة
  const clearAuthError = () => {
    dispatch(clearError());
  };

  // دالة التحقق من الصلاحية
  const hasPermission = (permission: string): boolean => {
    if (!user || !user.role || !user.role.permissions) {
      return false;
    }
    return authHelpers.hasPermission(user.role.permissions, permission);
  };

  // دالة التحقق من الدور
  const hasRole = (roles: string[]): boolean => {
    if (!user || !user.role) {
      return false;
    }
    return authHelpers.hasRole(user.role.name, roles);
  };

  // دالة تحديث الرمز المميز
  const refreshUserToken = async () => {
    try {
      await dispatch(refreshToken()).unwrap();
    } catch (error) {
      await logout();
    }
  };

  // التحقق من المصادقة عند تحميل التطبيق
  useEffect(() => {
    checkAuthentication();
  }, []);

  // مراقبة تغييرات المسار للتحقق من الصلاحيات
  useEffect(() => {
    const handleRouteChange = (url: string) => {
      const isPublicRoute = publicRoutes.some(route => url.startsWith(route));
      const isProtectedRoute = protectedRoutes.some(route => url.startsWith(route));

      // إذا كان المسار محمي والمستخدم غير مصادق عليه
      if (isProtectedRoute && !isAuthenticated && !isLoading) {
        router.push(`/auth/login?returnUrl=${encodeURIComponent(url)}`);
        return;
      }

      // إذا كان المستخدم مصادق عليه ويحاول الوصول لصفحة عامة
      if (isPublicRoute && isAuthenticated) {
        router.push('/dashboard');
        return;
      }
    };

    // التحقق من المسار الحالي
    handleRouteChange(router.asPath);

    // الاستماع لتغييرات المسار
    router.events.on('routeChangeStart', handleRouteChange);

    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [router, isAuthenticated, isLoading]);

  // مراقبة انتهاء صلاحية الرمز المميز
  useEffect(() => {
    if (!isAuthenticated) return;

    const checkTokenExpiration = () => {
      const token = authHelpers.getToken();
      if (token && authHelpers.isTokenExpired(token)) {
        refreshUserToken();
      }
    };

    // فحص كل 5 دقائق
    const interval = setInterval(checkTokenExpiration, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  // مراقبة حالة الشبكة
  useEffect(() => {
    const handleOnline = () => {
      if (isAuthenticated) {
        checkAuthentication();
      }
    };

    const handleOffline = () => {
      // يمكن إضافة منطق للتعامل مع فقدان الاتصال
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isAuthenticated]);

  // عرض شاشة التحميل أثناء التحقق من المصادقة
  if (isLoading && !user) {
    return <LoadingScreen message="جاري التحقق من المصادقة..." />;
  }

  // قيم Context
  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    checkAuthentication,
    clearAuthError,
    hasPermission,
    hasRole,
    refreshUserToken,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook للتحقق من الصلاحيات
export const usePermission = (permission: string) => {
  const { hasPermission } = useAuth();
  return hasPermission(permission);
};

// Hook للتحقق من الأدوار
export const useRole = (roles: string[]) => {
  const { hasRole } = useAuth();
  return hasRole(roles);
};

// HOC للحماية بالصلاحيات
export const withPermission = <P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission: string,
  fallback?: React.ComponentType
) => {
  return (props: P) => {
    const hasPermission = usePermission(requiredPermission);
    
    if (!hasPermission) {
      if (fallback) {
        const FallbackComponent = fallback;
        return <FallbackComponent />;
      }
      return (
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <h3>ليس لديك صلاحية للوصول إلى هذه الصفحة</h3>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};

// HOC للحماية بالأدوار
export const withRole = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRoles: string[],
  fallback?: React.ComponentType
) => {
  return (props: P) => {
    const hasRole = useRole(requiredRoles);
    
    if (!hasRole) {
      if (fallback) {
        const FallbackComponent = fallback;
        return <FallbackComponent />;
      }
      return (
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <h3>ليس لديك الدور المطلوب للوصول إلى هذه الصفحة</h3>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};

export default AuthContext;
