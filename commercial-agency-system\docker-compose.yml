version: '3.8'

services:
  # قاعدة البيانات MySQL
  mysql:
    image: mysql:8.0
    container_name: commercial_agency_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: ${DB_NAME:-commercial_agency_db}
      MYSQL_USER: ${DB_USER:-commercial_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-commercial123456}
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/config/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - commercial_agency_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis للتخزين المؤقت والجلسات
  redis:
    image: redis:7-alpine
    container_name: commercial_agency_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./database/config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - commercial_agency_network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # الخادم الخلفي (Backend)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: commercial_agency_backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: ${BACKEND_PORT:-5000}
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${DB_NAME:-commercial_agency_db}
      DB_USER: ${DB_USER:-commercial_user}
      DB_PASSWORD: ${DB_PASSWORD:-commercial123456}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-here}
      JWT_EXPIRE: ${JWT_EXPIRE:-24h}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-your-super-secret-refresh-key-here}
      JWT_REFRESH_EXPIRE: ${JWT_REFRESH_EXPIRE:-7d}
      SESSION_SECRET: ${SESSION_SECRET:-your-super-secret-session-key-here}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
      UPLOAD_PATH: ${UPLOAD_PATH:-/app/uploads}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      TIMEZONE: ${TIMEZONE:-Asia/Riyadh}
      DEFAULT_CURRENCY: ${DEFAULT_CURRENCY:-SAR}
    ports:
      - "${BACKEND_PORT:-5000}:5000"
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
      - backend_reports:/app/reports
      - backend_backups:/app/backups
      - /app/node_modules
    networks:
      - commercial_agency_network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # الواجهة الأمامية (Frontend)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: commercial_agency_frontend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:5000/api}
      NEXT_PUBLIC_APP_NAME: ${NEXT_PUBLIC_APP_NAME:-نظام الوكالة التجارية المتكامل}
      NEXT_PUBLIC_APP_VERSION: ${NEXT_PUBLIC_APP_VERSION:-1.0.0}
      NEXT_PUBLIC_ENVIRONMENT: ${NODE_ENV:-development}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - commercial_agency_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # Nginx كخادم ويب وموزع الأحمال
  nginx:
    image: nginx:alpine
    container_name: commercial_agency_nginx
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - commercial_agency_network
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      timeout: 10s
      retries: 3

  # phpMyAdmin لإدارة قاعدة البيانات
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: commercial_agency_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${DB_USER:-commercial_user}
      PMA_PASSWORD: ${DB_PASSWORD:-commercial123456}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root123456}
      PMA_ARBITRARY: 1
    ports:
      - "${PHPMYADMIN_PORT:-8080}:80"
    networks:
      - commercial_agency_network
    depends_on:
      mysql:
        condition: service_healthy
    profiles:
      - tools

  # Redis Commander لإدارة Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: commercial_agency_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    networks:
      - commercial_agency_network
    depends_on:
      - redis
    profiles:
      - tools

  # Elasticsearch للبحث المتقدم (اختياري)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: commercial_agency_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "${ELASTICSEARCH_PORT:-9200}:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - commercial_agency_network
    profiles:
      - search

  # Kibana لتصور البيانات (اختياري)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: commercial_agency_kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    networks:
      - commercial_agency_network
    depends_on:
      - elasticsearch
    profiles:
      - search

# الشبكات
networks:
  commercial_agency_network:
    driver: bridge
    name: commercial_agency_network

# وحدات التخزين
volumes:
  mysql_data:
    name: commercial_agency_mysql_data
  redis_data:
    name: commercial_agency_redis_data
  elasticsearch_data:
    name: commercial_agency_elasticsearch_data
  backend_uploads:
    name: commercial_agency_backend_uploads
  backend_logs:
    name: commercial_agency_backend_logs
  backend_reports:
    name: commercial_agency_backend_reports
  backend_backups:
    name: commercial_agency_backend_backups
  nginx_logs:
    name: commercial_agency_nginx_logs
