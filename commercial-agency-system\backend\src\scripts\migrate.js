#!/usr/bin/env node

/**
 * برنامج نصي لمزامنة قاعدة البيانات
 * Database Migration Script
 */

const dotenv = require('dotenv');
const path = require('path');

// تحميل متغيرات البيئة
dotenv.config({ path: path.join(__dirname, '../../.env') });

const { syncDatabase, validateDatabase } = require('../models');
const { initializeDatabase } = require('../config/database');

// خيارات سطر الأوامر
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force') || args.includes('-f'),
  alter: args.includes('--alter') || args.includes('-a'),
  validate: args.includes('--validate') || args.includes('-v'),
  help: args.includes('--help') || args.includes('-h')
};

// عرض المساعدة
const showHelp = () => {
  console.log(`
🔧 برنامج مزامنة قاعدة البيانات - Commercial Agency System

الاستخدام:
  node migrate.js [خيارات]

الخيارات:
  -f, --force      إعادة إنشاء جميع الجداول (حذف البيانات الموجودة)
  -a, --alter      تعديل الجداول الموجودة لتتطابق مع النماذج
  -v, --validate   التحقق من صحة قاعدة البيانات فقط
  -h, --help       عرض هذه المساعدة

أمثلة:
  node migrate.js                    # مزامنة عادية
  node migrate.js --force            # إعادة إنشاء كامل
  node migrate.js --alter            # تعديل الجداول الموجودة
  node migrate.js --validate         # التحقق من الصحة فقط

⚠️  تحذير: استخدام --force سيحذف جميع البيانات الموجودة!
  `);
};

// دالة المزامنة الرئيسية
const runMigration = async () => {
  try {
    console.log('🚀 بدء عملية مزامنة قاعدة البيانات...');
    console.log(`📅 التاريخ والوقت: ${new Date().toLocaleString('ar-SA')}`);
    console.log(`🌍 البيئة: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🗄️  قاعدة البيانات: ${process.env.DB_NAME}`);
    
    if (options.force) {
      console.log('⚠️  تحذير: سيتم حذف جميع البيانات الموجودة!');
      
      // انتظار تأكيد المستخدم في بيئة الإنتاج
      if (process.env.NODE_ENV === 'production') {
        const readline = require('readline');
        const rl = readline.createInterface({
          input: process.stdin,
          output: process.stdout
        });
        
        const answer = await new Promise((resolve) => {
          rl.question('هل أنت متأكد من المتابعة؟ اكتب "نعم" للتأكيد: ', resolve);
        });
        
        rl.close();
        
        if (answer !== 'نعم') {
          console.log('❌ تم إلغاء العملية');
          process.exit(0);
        }
      }
    }
    
    // تهيئة قاعدة البيانات
    console.log('🔌 الاتصال بقاعدة البيانات...');
    await initializeDatabase();
    
    if (options.validate) {
      // التحقق من الصحة فقط
      console.log('🔍 التحقق من صحة قاعدة البيانات...');
      const isValid = await validateDatabase();
      
      if (isValid) {
        console.log('✅ قاعدة البيانات صحيحة ومكتملة');
      } else {
        console.log('❌ قاعدة البيانات تحتاج إلى مزامنة');
        process.exit(1);
      }
    } else {
      // تحديد خيارات المزامنة
      const syncOptions = {};
      
      if (options.force) {
        syncOptions.force = true;
        console.log('🔄 إعادة إنشاء جميع الجداول...');
      } else if (options.alter) {
        syncOptions.alter = true;
        console.log('🔧 تعديل الجداول الموجودة...');
      } else {
        console.log('📊 مزامنة عادية للجداول...');
      }
      
      // تنفيذ المزامنة
      await syncDatabase(syncOptions);
      
      // التحقق من النتيجة
      console.log('🔍 التحقق من نتيجة المزامنة...');
      const isValid = await validateDatabase();
      
      if (isValid) {
        console.log('✅ تمت المزامنة بنجاح');
      } else {
        console.log('⚠️  المزامنة مكتملة ولكن هناك مشاكل في التحقق');
      }
    }
    
    console.log('🎉 انتهت عملية المزامنة بنجاح');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ خطأ في المزامنة:', error.message);
    
    if (process.env.NODE_ENV === 'development') {
      console.error('📋 تفاصيل الخطأ:', error.stack);
    }
    
    process.exit(1);
  }
};

// دالة النسخ الاحتياطي قبل المزامنة
const createBackupBeforeMigration = async () => {
  try {
    if (options.force && process.env.NODE_ENV === 'production') {
      console.log('💾 إنشاء نسخة احتياطية قبل المزامنة...');
      
      const { createBackup } = require('../config/database');
      const backupCommand = await createBackup();
      
      console.log('📋 أمر النسخ الاحتياطي:', backupCommand);
      console.log('ℹ️  يرجى تنفيذ الأمر أعلاه يدوياً لإنشاء النسخة الاحتياطية');
      
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise((resolve) => {
        rl.question('هل تم إنشاء النسخة الاحتياطية؟ اكتب "نعم" للمتابعة: ', resolve);
      });
      
      rl.close();
      
      if (answer !== 'نعم') {
        console.log('❌ تم إلغاء العملية - يرجى إنشاء النسخة الاحتياطية أولاً');
        process.exit(0);
      }
    }
  } catch (error) {
    console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error.message);
    process.exit(1);
  }
};

// دالة تسجيل المزامنة
const logMigration = async (success = true, error = null) => {
  try {
    const fs = require('fs').promises;
    const logDir = path.join(__dirname, '../../logs');
    const logFile = path.join(logDir, 'migrations.log');
    
    // إنشاء مجلد السجلات إذا لم يكن موجوداً
    try {
      await fs.access(logDir);
    } catch {
      await fs.mkdir(logDir, { recursive: true });
    }
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      success,
      options,
      environment: process.env.NODE_ENV,
      database: process.env.DB_NAME,
      error: error ? error.message : null
    };
    
    const logLine = JSON.stringify(logEntry) + '\n';
    await fs.appendFile(logFile, logLine);
    
  } catch (logError) {
    console.error('⚠️  خطأ في تسجيل المزامنة:', logError.message);
  }
};

// تنفيذ البرنامج النصي
const main = async () => {
  if (options.help) {
    showHelp();
    process.exit(0);
  }
  
  try {
    await createBackupBeforeMigration();
    await runMigration();
    await logMigration(true);
  } catch (error) {
    await logMigration(false, error);
    throw error;
  }
};

// تشغيل البرنامج النصي
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ فشل في تنفيذ المزامنة:', error.message);
    process.exit(1);
  });
}

module.exports = {
  runMigration,
  createBackupBeforeMigration,
  logMigration
};
