import React from 'react';
import Document, { Html, Head, Main, NextScript, DocumentContext } from 'next/document';
import createEmotionServer from '@emotion/server/create-instance';
import theme from '@/styles/theme';
import createEmotionCache from '@/utils/createEmotionCache';

export default class MyDocument extends Document {
  static async getInitialProps(ctx: DocumentContext) {
    const originalRenderPage = ctx.renderPage;

    // يمكنك اعتبار مشاركة نفس emotion cache بين جميع SSR requests
    // لتحسين الأداء. ومع ذلك، كن على علم أن ذلك يمكن أن يكون له آثار جانبية عالمية.
    const cache = createEmotionCache();
    const { extractCriticalToChunks } = createEmotionServer(cache);

    ctx.renderPage = () =>
      originalRenderPage({
        enhanceApp: (App: any) =>
          function EnhanceApp(props) {
            return <App emotionCache={cache} {...props} />;
          },
      });

    const initialProps = await Document.getInitialProps(ctx);
    // هذا مهم. يمنع emotion من عرض CSS غير صحيح.
    // انظر https://github.com/mui-org/material-ui/issues/26561#issuecomment-855286153
    const emotionStyles = extractCriticalToChunks(initialProps.html);
    const emotionStyleTags = emotionStyles.styles.map((style) => (
      <style
        data-emotion={`${style.key} ${style.ids.join(' ')}`}
        key={style.key}
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{ __html: style.css }}
      />
    ));

    return {
      ...initialProps,
      emotionStyleTags,
    };
  }

  render() {
    return (
      <Html lang="ar" dir="rtl">
        <Head>
          {/* PWA primary color */}
          <meta name="theme-color" content={theme.palette.primary.main} />
          
          {/* Emotion styles */}
          <meta name="emotion-insertion-point" content="" />
          {(this.props as any).emotionStyleTags}
          
          {/* خطوط إضافية */}
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
          
          {/* أيقونات Material Icons */}
          <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/icon?family=Material+Icons"
          />
          
          {/* CSS مخصص للطباعة */}
          <style jsx global>{`
            @media print {
              body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }
              
              .no-print {
                display: none !important;
              }
              
              .print-only {
                display: block !important;
              }
              
              @page {
                margin: 1cm;
                size: A4;
              }
            }
            
            /* تحسينات للغة العربية */
            body {
              font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              direction: rtl;
              text-align: right;
            }
            
            /* تحسينات الأداء */
            * {
              box-sizing: border-box;
            }
            
            /* تحسينات إمكانية الوصول */
            .sr-only {
              position: absolute;
              width: 1px;
              height: 1px;
              padding: 0;
              margin: -1px;
              overflow: hidden;
              clip: rect(0, 0, 0, 0);
              white-space: nowrap;
              border: 0;
            }
            
            /* تحسينات التمرير */
            ::-webkit-scrollbar {
              width: 8px;
              height: 8px;
            }
            
            ::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 4px;
            }
            
            ::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 4px;
            }
            
            ::-webkit-scrollbar-thumb:hover {
              background: #a8a8a8;
            }
            
            /* تحسينات التركيز */
            .focus-visible {
              outline: 2px solid ${theme.palette.primary.main};
              outline-offset: 2px;
            }
            
            /* تحسينات الحركة */
            @media (prefers-reduced-motion: reduce) {
              *,
              *::before,
              *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
              }
            }
            
            /* تحسينات الألوان للوضع المظلم */
            @media (prefers-color-scheme: dark) {
              :root {
                color-scheme: dark;
              }
            }
            
            /* تحسينات للشاشات عالية الكثافة */
            @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
              .high-dpi {
                /* تحسينات للشاشات عالية الكثافة */
              }
            }
          `}</style>
        </Head>
        <body>
          <Main />
          <NextScript />
          
          {/* نص بديل للمتصفحات التي لا تدعم JavaScript */}
          <noscript>
            <div style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              fontFamily: 'Cairo, sans-serif',
              textAlign: 'center',
              padding: '20px',
              zIndex: 9999
            }}>
              <h1 style={{ color: '#333', marginBottom: '20px' }}>
                JavaScript مطلوب
              </h1>
              <p style={{ color: '#666', fontSize: '16px', lineHeight: '1.5' }}>
                يتطلب هذا التطبيق تفعيل JavaScript في متصفحك للعمل بشكل صحيح.
                <br />
                يرجى تفعيل JavaScript وإعادة تحميل الصفحة.
              </p>
            </div>
          </noscript>
        </body>
      </Html>
    );
  }
}

MyDocument.getInitialProps = MyDocument.getInitialProps;
