-- نظام الوكالة التجارية المتكامل - قاعدة البيانات MySQL
-- Commercial Agency System - Complete Database Schema

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS commercial_agency_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE commercial_agency_db;

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    name_ar VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSO<PERSON>,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    avatar VARCHAR(255),
    role_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id),
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- جدول جلسات المستخدمين
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token_hash),
    INDEX idx_user_id (user_id)
);

-- جدول فئات المنتجات
CREATE TABLE product_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT NULL,
    image_url VARCHAR(255),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES product_categories(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_active (is_active)
);

-- جدول العلامات التجارية
CREATE TABLE brands (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    logo_url VARCHAR(255),
    website VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active)
);

-- جدول وحدات القياس
CREATE TABLE units (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    name_ar VARCHAR(50) NOT NULL,
    symbol VARCHAR(10),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المخازن
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT,
    city VARCHAR(50),
    phone VARCHAR(20),
    manager_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الموردين
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    company_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    fax VARCHAR(20),
    website VARCHAR(100),
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50) DEFAULT 'Saudi Arabia',
    postal_code VARCHAR(10),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    payment_terms INT DEFAULT 30,
    currency VARCHAR(3) DEFAULT 'SAR',
    supplier_type ENUM('local', 'international') DEFAULT 'local',
    rating DECIMAL(3,2) DEFAULT 0,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_active (is_active),
    INDEX idx_company_name (company_name)
);

-- جدول جهات الاتصال للموردين
CREATE TABLE supplier_contacts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    position VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    is_primary BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    INDEX idx_supplier_id (supplier_id)
);

-- جدول البنوك للموردين
CREATE TABLE supplier_banks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_id INT NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(100),
    iban VARCHAR(50),
    swift_code VARCHAR(20),
    branch VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    INDEX idx_supplier_id (supplier_id)
);

-- جدول العملاء
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    customer_type ENUM('individual', 'company') NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    company_name VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    national_id VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50) DEFAULT 'Saudi Arabia',
    postal_code VARCHAR(10),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    payment_terms INT DEFAULT 30,
    customer_category ENUM('retail', 'wholesale', 'vip') DEFAULT 'retail',
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'SAR',
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_customer_code (customer_code),
    INDEX idx_active (is_active),
    INDEX idx_customer_type (customer_type)
);

-- جدول عناوين العملاء
CREATE TABLE customer_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    address_type ENUM('billing', 'shipping', 'both') DEFAULT 'both',
    address_line1 TEXT NOT NULL,
    address_line2 TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    country VARCHAR(50) DEFAULT 'Saudi Arabia',
    postal_code VARCHAR(10),
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id)
);

-- جدول المنتجات
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_code VARCHAR(50) UNIQUE NOT NULL,
    barcode VARCHAR(50) UNIQUE,
    name VARCHAR(200) NOT NULL,
    name_ar VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INT,
    brand_id INT,
    unit_id INT NOT NULL,
    purchase_price DECIMAL(15,2) DEFAULT 0,
    selling_price DECIMAL(15,2) DEFAULT 0,
    min_selling_price DECIMAL(15,2) DEFAULT 0,
    wholesale_price DECIMAL(15,2) DEFAULT 0,
    cost_price DECIMAL(15,2) DEFAULT 0,
    weight DECIMAL(10,3),
    dimensions VARCHAR(50),
    color VARCHAR(50),
    size VARCHAR(50),
    warranty_period INT DEFAULT 0,
    reorder_level INT DEFAULT 0,
    max_stock_level INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    is_serialized BOOLEAN DEFAULT FALSE,
    track_inventory BOOLEAN DEFAULT TRUE,
    tax_rate DECIMAL(5,2) DEFAULT 15,
    image_url VARCHAR(255),
    gallery JSON,
    specifications JSON,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id),
    FOREIGN KEY (brand_id) REFERENCES brands(id),
    FOREIGN KEY (unit_id) REFERENCES units(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_product_code (product_code),
    INDEX idx_barcode (barcode),
    INDEX idx_category_id (category_id),
    INDEX idx_active (is_active),
    FULLTEXT idx_search (name, name_ar, description)
);

-- جدول المخزون
CREATE TABLE inventory (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    warehouse_id INT DEFAULT 1,
    quantity_on_hand DECIMAL(15,3) DEFAULT 0,
    quantity_reserved DECIMAL(15,3) DEFAULT 0,
    quantity_available DECIMAL(15,3) GENERATED ALWAYS AS (quantity_on_hand - quantity_reserved) STORED,
    last_purchase_price DECIMAL(15,2),
    average_cost DECIMAL(15,2),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    UNIQUE KEY unique_product_warehouse (product_id, warehouse_id),
    INDEX idx_product_id (product_id),
    INDEX idx_warehouse_id (warehouse_id)
);

-- جدول حركات المخزون
CREATE TABLE inventory_movements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    warehouse_id INT DEFAULT 1,
    movement_type ENUM('in', 'out', 'adjustment', 'transfer') NOT NULL,
    reference_type ENUM('purchase', 'sale', 'adjustment', 'transfer', 'return', 'opening_balance') NOT NULL,
    reference_id INT,
    quantity DECIMAL(15,3) NOT NULL,
    unit_cost DECIMAL(15,2),
    total_cost DECIMAL(15,2),
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_product_id (product_id),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_created_at (created_at)
);

-- جدول طلبات الشراء
CREATE TABLE purchase_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    po_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id INT NOT NULL,
    warehouse_id INT DEFAULT 1,
    order_date DATE NOT NULL,
    expected_delivery_date DATE,
    delivery_date DATE,
    status ENUM('draft', 'pending', 'approved', 'sent', 'partial', 'completed', 'cancelled') DEFAULT 'draft',
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    shipping_cost DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'SAR',
    exchange_rate DECIMAL(10,4) DEFAULT 1,
    payment_terms INT DEFAULT 30,
    delivery_address TEXT,
    terms_conditions TEXT,
    notes TEXT,
    created_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_po_number (po_number),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_status (status),
    INDEX idx_order_date (order_date)
);

-- جدول تفاصيل طلبات الشراء
CREATE TABLE purchase_order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    purchase_order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 15,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    received_quantity DECIMAL(15,3) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_purchase_order_id (purchase_order_id),
    INDEX idx_product_id (product_id)
);

-- جدول فواتير الشراء
CREATE TABLE purchase_invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_invoice_number VARCHAR(50),
    supplier_id INT NOT NULL,
    purchase_order_id INT,
    warehouse_id INT DEFAULT 1,
    invoice_date DATE NOT NULL,
    due_date DATE,
    status ENUM('draft', 'pending', 'approved', 'paid', 'partial', 'overdue', 'cancelled') DEFAULT 'draft',
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    balance_due DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    currency VARCHAR(3) DEFAULT 'SAR',
    exchange_rate DECIMAL(10,4) DEFAULT 1,
    notes TEXT,
    created_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_status (status),
    INDEX idx_invoice_date (invoice_date)
);

-- جدول تفاصيل فواتير الشراء
CREATE TABLE purchase_invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    purchase_invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 15,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_invoice_id) REFERENCES purchase_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_purchase_invoice_id (purchase_invoice_id),
    INDEX idx_product_id (product_id)
);

-- جدول عروض الأسعار
CREATE TABLE quotations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quotation_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT NOT NULL,
    quotation_date DATE NOT NULL,
    valid_until DATE,
    status ENUM('draft', 'sent', 'accepted', 'rejected', 'expired', 'converted') DEFAULT 'draft',
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'SAR',
    payment_terms INT DEFAULT 30,
    delivery_terms TEXT,
    terms_conditions TEXT,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_quotation_number (quotation_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_quotation_date (quotation_date)
);

-- جدول تفاصيل عروض الأسعار
CREATE TABLE quotation_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quotation_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 15,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quotation_id) REFERENCES quotations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_quotation_id (quotation_id),
    INDEX idx_product_id (product_id)
);

-- جدول فواتير البيع
CREATE TABLE sales_invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT NOT NULL,
    quotation_id INT,
    warehouse_id INT DEFAULT 1,
    invoice_date DATE NOT NULL,
    due_date DATE,
    status ENUM('draft', 'sent', 'paid', 'partial', 'overdue', 'cancelled') DEFAULT 'draft',
    subtotal DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    shipping_cost DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    balance_due DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    currency VARCHAR(3) DEFAULT 'SAR',
    payment_method ENUM('cash', 'credit', 'bank_transfer', 'check', 'card') DEFAULT 'cash',
    shipping_address TEXT,
    terms_conditions TEXT,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (quotation_id) REFERENCES quotations(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_invoice_date (invoice_date)
);

-- جدول تفاصيل فواتير البيع
CREATE TABLE sales_invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sales_invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    cost_price DECIMAL(15,2) DEFAULT 0,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 15,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    profit DECIMAL(15,2) GENERATED ALWAYS AS (line_total - (quantity * cost_price)) STORED,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sales_invoice_id) REFERENCES sales_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_sales_invoice_id (sales_invoice_id),
    INDEX idx_product_id (product_id)
);

-- جدول الحسابات البنكية
CREATE TABLE bank_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_name VARCHAR(100) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    iban VARCHAR(50),
    swift_code VARCHAR(20),
    branch VARCHAR(100),
    currency VARCHAR(3) DEFAULT 'SAR',
    opening_balance DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    account_type ENUM('checking', 'savings', 'credit') DEFAULT 'checking',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_account_number (account_number),
    INDEX idx_active (is_active)
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    payment_type ENUM('received', 'paid') NOT NULL,
    reference_type ENUM('sales_invoice', 'purchase_invoice', 'advance', 'refund', 'expense') NOT NULL,
    reference_id INT,
    customer_id INT,
    supplier_id INT,
    amount DECIMAL(15,2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'check', 'card', 'online') NOT NULL,
    payment_date DATE NOT NULL,
    bank_account_id INT,
    check_number VARCHAR(50),
    check_date DATE,
    check_bank VARCHAR(100),
    reference_number VARCHAR(100),
    currency VARCHAR(3) DEFAULT 'SAR',
    exchange_rate DECIMAL(10,4) DEFAULT 1,
    notes TEXT,
    status ENUM('pending', 'completed', 'cancelled', 'bounced') DEFAULT 'completed',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_payment_number (payment_number),
    INDEX idx_payment_type (payment_type),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_payment_date (payment_date)
);

-- جدول أرصدة العملاء
CREATE TABLE customer_balances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0,
    credit_limit DECIMAL(15,2) DEFAULT 0,
    last_transaction_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    UNIQUE KEY unique_customer (customer_id)
);

-- جدول أرصدة الموردين
CREATE TABLE supplier_balances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_id INT NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0,
    last_transaction_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    UNIQUE KEY unique_supplier (supplier_id)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
);

-- جدول سجل العمليات
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_created_at (created_at)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- جدول المرفقات
CREATE TABLE attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reference_type VARCHAR(50) NOT NULL,
    reference_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100),
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_uploaded_by (uploaded_by)
);

-- جدول المصروفات
CREATE TABLE expenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_number VARCHAR(50) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    expense_date DATE NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'check', 'card') NOT NULL,
    bank_account_id INT,
    supplier_id INT,
    receipt_number VARCHAR(50),
    notes TEXT,
    created_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_expense_date (expense_date),
    INDEX idx_category (category)
);

-- إدراج البيانات الأساسية

-- إدراج الأدوار
INSERT INTO roles (name, name_ar, description, permissions) VALUES
('admin', 'مدير النظام', 'مدير النظام - صلاحيات كاملة', '["all"]'),
('sales_manager', 'مدير المبيعات', 'مدير المبيعات - إدارة العملاء والمبيعات', '["customers", "sales", "quotations", "reports"]'),
('purchase_manager', 'مدير المشتريات', 'مدير المشتريات - إدارة الموردين والمشتريات', '["suppliers", "purchases", "inventory", "reports"]'),
('accountant', 'محاسب', 'المحاسب - إدارة المالية والمديونية', '["payments", "financial_reports", "customers", "suppliers"]'),
('sales_employee', 'موظف مبيعات', 'موظف المبيعات - عمليات البيع فقط', '["sales", "quotations", "customers_view"]'),
('warehouse_keeper', 'أمين المخزن', 'أمين المخزن - إدارة المخزون', '["inventory", "products", "warehouse_operations"]');

-- إدراج المستخدم الافتراضي (admin)
INSERT INTO users (username, email, password_hash, first_name, last_name, role_id, is_active, email_verified) VALUES
('admin', '<EMAIL>', '$2b$10$rOzJaHq8GQeWqx8tZVjQKOYjDaP5QJmZvXzJqKqJqKqJqKqJqKqJq', 'مدير', 'النظام', 1, TRUE, TRUE);

-- إدراج وحدات القياس الأساسية
INSERT INTO units (name, name_ar, symbol, description) VALUES
('piece', 'قطعة', 'pcs', 'قطعة واحدة'),
('kilogram', 'كيلوجرام', 'kg', 'وحدة الوزن بالكيلوجرام'),
('gram', 'جرام', 'g', 'وحدة الوزن بالجرام'),
('liter', 'لتر', 'L', 'وحدة الحجم باللتر'),
('meter', 'متر', 'm', 'وحدة الطول بالمتر'),
('box', 'صندوق', 'box', 'صندوق يحتوي على عدة قطع'),
('carton', 'كرتون', 'ctn', 'كرتون يحتوي على عدة قطع'),
('dozen', 'دزينة', 'dz', 'دزينة - 12 قطعة');

-- إدراج المخزن الافتراضي
INSERT INTO warehouses (name, name_ar, code, address, city, is_active) VALUES
('المخزن الرئيسي', 'Main Warehouse', 'WH001', 'المخزن الرئيسي للشركة', 'الرياض', TRUE);

-- إدراج فئات المنتجات الأساسية
INSERT INTO product_categories (name, name_ar, description, sort_order) VALUES
('إلكترونيات', 'Electronics', 'الأجهزة الإلكترونية والكهربائية', 1),
('أجهزة كمبيوتر', 'Computers', 'أجهزة الكمبيوتر واللابتوب', 2),
('هواتف ذكية', 'Smartphones', 'الهواتف الذكية والأجهزة اللوحية', 3),
('أجهزة منزلية', 'Home Appliances', 'الأجهزة المنزلية والكهربائية', 4),
('قطع غيار', 'Spare Parts', 'قطع الغيار والاكسسوارات', 5);

-- إدراج العلامات التجارية الأساسية
INSERT INTO brands (name, name_ar, description) VALUES
('Samsung', 'سامسونج', 'شركة سامسونج للإلكترونيات'),
('Apple', 'آبل', 'شركة آبل للتكنولوجيا'),
('HP', 'إتش بي', 'شركة إتش بي للكمبيوتر'),
('Dell', 'ديل', 'شركة ديل للكمبيوتر'),
('LG', 'إل جي', 'شركة إل جي للإلكترونيات');

-- إدراج الحسابات البنكية الأساسية
INSERT INTO bank_accounts (account_name, bank_name, account_number, currency, opening_balance, current_balance) VALUES
('الحساب الجاري الرئيسي', 'البنك الأهلي السعودي', '**********', 'SAR', 0, 0),
('حساب الصندوق', 'نقدي', 'CASH001', 'SAR', 0, 0);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, category, description) VALUES
('company_name', 'الوكالة التجارية المتكاملة', 'string', 'company', 'اسم الشركة'),
('company_name_en', 'Integrated Commercial Agency', 'string', 'company', 'اسم الشركة بالإنجليزية'),
('company_address', 'الرياض، المملكة العربية السعودية', 'string', 'company', 'عنوان الشركة'),
('company_phone', '+966-11-1234567', 'string', 'company', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'string', 'company', 'بريد الشركة الإلكتروني'),
('tax_number', '**********12345', 'string', 'company', 'الرقم الضريبي للشركة'),
('commercial_register', '1010123456', 'string', 'company', 'رقم السجل التجاري'),
('default_currency', 'SAR', 'string', 'system', 'العملة الافتراضية'),
('default_tax_rate', '15', 'number', 'system', 'معدل الضريبة الافتراضي'),
('low_stock_threshold', '10', 'number', 'inventory', 'حد التنبيه للمخزون المنخفض'),
('auto_generate_codes', 'true', 'boolean', 'system', 'توليد الأكواد تلقائياً'),
('backup_frequency', 'daily', 'string', 'system', 'تكرار النسخ الاحتياطي');

-- إنشاء المؤشرات الإضافية لتحسين الأداء
CREATE INDEX idx_products_search ON products(name, name_ar, product_code);
CREATE INDEX idx_customers_search ON customers(first_name, last_name, company_name, customer_code);
CREATE INDEX idx_suppliers_search ON suppliers(company_name, supplier_code);
CREATE INDEX idx_invoices_date_range ON sales_invoices(invoice_date, status);
CREATE INDEX idx_purchase_invoices_date_range ON purchase_invoices(invoice_date, status);
CREATE INDEX idx_payments_date_range ON payments(payment_date, payment_type);

-- إنشاء الـ Views للتقارير
CREATE VIEW v_customer_summary AS
SELECT
    c.id,
    c.customer_code,
    CASE
        WHEN c.customer_type = 'individual' THEN CONCAT(c.first_name, ' ', c.last_name)
        ELSE c.company_name
    END as customer_name,
    c.customer_type,
    c.customer_category,
    c.credit_limit,
    COALESCE(cb.balance, 0) as current_balance,
    COUNT(si.id) as total_invoices,
    COALESCE(SUM(si.total_amount), 0) as total_sales,
    COALESCE(SUM(si.balance_due), 0) as outstanding_balance,
    MAX(si.invoice_date) as last_invoice_date
FROM customers c
LEFT JOIN customer_balances cb ON c.id = cb.customer_id
LEFT JOIN sales_invoices si ON c.id = si.customer_id
WHERE c.is_active = TRUE
GROUP BY c.id, c.customer_code, customer_name, c.customer_type, c.customer_category, c.credit_limit, cb.balance;

CREATE VIEW v_supplier_summary AS
SELECT
    s.id,
    s.supplier_code,
    s.company_name,
    s.supplier_type,
    s.credit_limit,
    COALESCE(sb.balance, 0) as current_balance,
    COUNT(pi.id) as total_invoices,
    COALESCE(SUM(pi.total_amount), 0) as total_purchases,
    COALESCE(SUM(pi.balance_due), 0) as outstanding_balance,
    MAX(pi.invoice_date) as last_invoice_date
FROM suppliers s
LEFT JOIN supplier_balances sb ON s.id = sb.supplier_id
LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
WHERE s.is_active = TRUE
GROUP BY s.id, s.supplier_code, s.company_name, s.supplier_type, s.credit_limit, sb.balance;

CREATE VIEW v_inventory_summary AS
SELECT
    p.id,
    p.product_code,
    p.name,
    p.name_ar,
    pc.name as category_name,
    b.name as brand_name,
    u.name as unit_name,
    p.selling_price,
    p.purchase_price,
    COALESCE(i.quantity_on_hand, 0) as quantity_on_hand,
    COALESCE(i.quantity_reserved, 0) as quantity_reserved,
    COALESCE(i.quantity_available, 0) as quantity_available,
    p.reorder_level,
    CASE
        WHEN COALESCE(i.quantity_available, 0) <= p.reorder_level THEN 'low_stock'
        WHEN COALESCE(i.quantity_available, 0) = 0 THEN 'out_of_stock'
        ELSE 'in_stock'
    END as stock_status
FROM products p
LEFT JOIN product_categories pc ON p.category_id = pc.id
LEFT JOIN brands b ON p.brand_id = b.id
LEFT JOIN units u ON p.unit_id = u.id
LEFT JOIN inventory i ON p.id = i.product_id AND i.warehouse_id = 1
WHERE p.is_active = TRUE;
