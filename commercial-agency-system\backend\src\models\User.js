const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { hashPassword, comparePassword } = require('../config/auth');

// نموذج المستخدم
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50],
      notEmpty: true,
      isAlphanumeric: true
    }
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
      notEmpty: true
    }
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'password_hash'
  },
  first_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'first_name',
    validate: {
      len: [2, 50],
      notEmpty: true
    }
  },
  last_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'last_name',
    validate: {
      len: [2, 50],
      notEmpty: true
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^[\+]?[0-9\-\(\)\s]+$/
    }
  },
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'role_id'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  email_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'email_verified'
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_login'
  },
  login_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'login_attempts'
  },
  locked_until: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'locked_until'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'updated_at'
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['username']
    },
    {
      unique: true,
      fields: ['email']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['role_id']
    }
  ],
  hooks: {
    beforeCreate: async (user) => {
      if (user.password_hash) {
        user.password_hash = await hashPassword(user.password_hash);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password_hash')) {
        user.password_hash = await hashPassword(user.password_hash);
      }
    }
  }
});

// الدوال الإضافية للنموذج
User.prototype.getFullName = function() {
  return `${this.first_name} ${this.last_name}`;
};

User.prototype.checkPassword = async function(password) {
  return await comparePassword(password, this.password_hash);
};

User.prototype.isLocked = function() {
  return this.locked_until && this.locked_until > new Date();
};

User.prototype.incrementLoginAttempts = async function() {
  const maxAttempts = 5;
  const lockTime = 30 * 60 * 1000; // 30 دقيقة
  
  this.login_attempts += 1;
  
  if (this.login_attempts >= maxAttempts) {
    this.locked_until = new Date(Date.now() + lockTime);
  }
  
  await this.save();
};

User.prototype.resetLoginAttempts = async function() {
  this.login_attempts = 0;
  this.locked_until = null;
  this.last_login = new Date();
  await this.save();
};

User.prototype.toJSON = function() {
  const values = { ...this.get() };
  delete values.password_hash;
  return values;
};

// الدوال الثابتة
User.findByUsername = async function(username) {
  return await this.findOne({
    where: { username, is_active: true }
  });
};

User.findByEmail = async function(email) {
  return await this.findOne({
    where: { email, is_active: true }
  });
};

User.findActiveUsers = async function(options = {}) {
  return await this.findAll({
    where: { is_active: true },
    ...options
  });
};

User.createUser = async function(userData) {
  try {
    const user = await this.create(userData);
    return user;
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0].path;
      throw new Error(`${field === 'username' ? 'اسم المستخدم' : 'البريد الإلكتروني'} مستخدم بالفعل`);
    }
    throw error;
  }
};

User.updateUser = async function(id, userData) {
  const user = await this.findByPk(id);
  if (!user) {
    throw new Error('المستخدم غير موجود');
  }
  
  await user.update(userData);
  return user;
};

User.deleteUser = async function(id) {
  const user = await this.findByPk(id);
  if (!user) {
    throw new Error('المستخدم غير موجود');
  }
  
  // حذف منطقي
  await user.update({ is_active: false });
  return user;
};

User.searchUsers = async function(searchTerm, options = {}) {
  const { Op } = require('sequelize');
  
  return await this.findAll({
    where: {
      is_active: true,
      [Op.or]: [
        { username: { [Op.like]: `%${searchTerm}%` } },
        { first_name: { [Op.like]: `%${searchTerm}%` } },
        { last_name: { [Op.like]: `%${searchTerm}%` } },
        { email: { [Op.like]: `%${searchTerm}%` } }
      ]
    },
    ...options
  });
};

User.getUserStats = async function() {
  const { Op } = require('sequelize');
  
  const totalUsers = await this.count();
  const activeUsers = await this.count({ where: { is_active: true } });
  const inactiveUsers = await this.count({ where: { is_active: false } });
  const recentLogins = await this.count({
    where: {
      last_login: {
        [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // آخر 24 ساعة
      }
    }
  });
  
  return {
    total: totalUsers,
    active: activeUsers,
    inactive: inactiveUsers,
    recentLogins
  };
};

// التحقق من الصلاحيات
User.prototype.hasPermission = function(permission) {
  // سيتم تنفيذ هذا عند إضافة نموذج الأدوار
  return true;
};

User.prototype.hasRole = function(roleName) {
  // سيتم تنفيذ هذا عند إضافة نموذج الأدوار
  return true;
};

// تصدير النموذج
module.exports = User;
