{"name": "commercial-agency-system", "version": "1.0.0", "description": "نظام الوكالة التجارية المتكامل - حل شامل لإدارة الأعمال التجارية", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "db:backup": "cd backend && npm run db:backup", "db:restore": "cd backend && npm run db:restore", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:frontend": "cd frontend && rm -rf node_modules .next", "setup": "npm run install:all && npm run db:migrate && npm run db:seed", "docs:generate": "cd backend && npm run docs:generate", "docs:serve": "cd backend && npm run docs:serve", "security:audit": "npm audit && npm run security:audit:backend && npm run security:audit:frontend", "security:audit:backend": "cd backend && npm audit", "security:audit:frontend": "cd frontend && npm audit", "update:deps": "npm update && npm run update:deps:backend && npm run update:deps:frontend", "update:deps:backend": "cd backend && npm update", "update:deps:frontend": "cd frontend && npm update"}, "keywords": ["commercial-agency", "business-management", "inventory-management", "sales-management", "purchase-management", "customer-management", "supplier-management", "accounting", "erp", "crm", "arabic", "nodejs", "react", "mysql"], "author": {"name": "Commercial Agency System Team", "email": "<EMAIL>", "url": "https://commercial-agency.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/commercial-agency/system.git"}, "bugs": {"url": "https://github.com/commercial-agency/system/issues"}, "homepage": "https://commercial-agency.com", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "workspaces": ["backend", "frontend"], "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"backend/**/*.{js,ts}": ["cd backend && npm run lint:fix", "cd backend && npm run test"], "frontend/**/*.{js,jsx,ts,tsx}": ["cd frontend && npm run lint:fix", "cd frontend && npm run test"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}