# ==============================================
# نظام الوكالة التجارية المتكامل - متغيرات البيئة
# Commercial Agency System - Environment Variables
# ==============================================

# بيئة التطبيق
NODE_ENV=development
TIMEZONE=Asia/Riyadh
DEFAULT_CURRENCY=SAR
DEFAULT_LANGUAGE=ar

# ==============================================
# إعدادات قاعدة البيانات - Database Settings
# ==============================================

# MySQL Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=commercial_agency_db
DB_USER=commercial_user
DB_PASSWORD=commercial123456
DB_ROOT_PASSWORD=root123456

# Database Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# ==============================================
# إعدادات Redis - Redis Settings
# ==============================================

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ==============================================
# إعدادات الخادم - Server Settings
# ==============================================

# Backend Server
BACKEND_PORT=5000
BACKEND_HOST=localhost

# Frontend Server
FRONTEND_PORT=3000
FRONTEND_HOST=localhost

# Nginx Ports
HTTP_PORT=80
HTTPS_PORT=443

# ==============================================
# إعدادات الأمان - Security Settings
# ==============================================

# JWT Settings
JWT_SECRET=your-super-secret-jwt-key-here-change-this-in-production
JWT_EXPIRE=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-change-this-in-production
JWT_REFRESH_EXPIRE=7d

# Session Settings
SESSION_SECRET=your-super-secret-session-key-here-change-this-in-production
SESSION_EXPIRE=86400000

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here

# CORS Settings
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# ==============================================
# إعدادات التطبيق - Application Settings
# ==============================================

# API Settings
API_PREFIX=/api
API_VERSION=v1
API_RATE_LIMIT=100
API_RATE_WINDOW=900000

# Upload Settings
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# ==============================================
# إعدادات البريد الإلكتروني - Email Settings
# ==============================================

# SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email Templates
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=نظام الوكالة التجارية

# ==============================================
# إعدادات الرسائل النصية - SMS Settings
# ==============================================

# SMS Provider (Twilio example)
SMS_PROVIDER=twilio
SMS_ACCOUNT_SID=your-twilio-account-sid
SMS_AUTH_TOKEN=your-twilio-auth-token
SMS_FROM_NUMBER=+**********

# ==============================================
# إعدادات الدفع - Payment Settings
# ==============================================

# Payment Gateway (Example: Stripe)
PAYMENT_PROVIDER=stripe
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# ==============================================
# إعدادات التخزين السحابي - Cloud Storage
# ==============================================

# AWS S3 Settings
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=commercial-agency-bucket

# ==============================================
# إعدادات المراقبة - Monitoring Settings
# ==============================================

# Application Monitoring
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=development

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# ==============================================
# إعدادات التطوير - Development Settings
# ==============================================

# Debug Settings
DEBUG=true
VERBOSE_LOGGING=false

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH=/api-docs

# Hot Reload
HOT_RELOAD=true

# ==============================================
# إعدادات الإنتاج - Production Settings
# ==============================================

# SSL Settings
SSL_ENABLED=false
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Compression
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# Caching
CACHE_ENABLED=true
CACHE_TTL=3600

# ==============================================
# إعدادات النسخ الاحتياطي - Backup Settings
# ==============================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups

# ==============================================
# إعدادات الأدوات - Tools Settings
# ==============================================

# phpMyAdmin
PHPMYADMIN_PORT=8080

# Redis Commander
REDIS_COMMANDER_PORT=8081

# Elasticsearch (Optional)
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_ENABLED=false

# Kibana (Optional)
KIBANA_PORT=5601
KIBANA_ENABLED=false

# ==============================================
# إعدادات الشركة - Company Settings
# ==============================================

# Company Information
COMPANY_NAME=الوكالة التجارية المتكاملة
COMPANY_NAME_EN=Integrated Commercial Agency
COMPANY_ADDRESS=الرياض، المملكة العربية السعودية
COMPANY_PHONE=+966-11-1234567
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=https://commercial-agency.com
COMPANY_TAX_NUMBER=**********12345
COMPANY_COMMERCIAL_REGISTER=1010123456

# ==============================================
# إعدادات النظام - System Settings
# ==============================================

# System Configuration
SYSTEM_MAINTENANCE_MODE=false
SYSTEM_REGISTRATION_ENABLED=false
SYSTEM_EMAIL_VERIFICATION_REQUIRED=true
SYSTEM_TWO_FACTOR_ENABLED=false

# Default Settings
DEFAULT_TAX_RATE=15
DEFAULT_PAYMENT_TERMS=30
DEFAULT_CURRENCY_SYMBOL=ر.س
DEFAULT_DATE_FORMAT=DD/MM/YYYY
DEFAULT_TIME_FORMAT=HH:mm

# ==============================================
# إعدادات الواجهة - Frontend Settings
# ==============================================

# Next.js Settings
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_APP_NAME=نظام الوكالة التجارية المتكامل
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# Theme Settings
NEXT_PUBLIC_DEFAULT_THEME=light
NEXT_PUBLIC_PRIMARY_COLOR=#2196f3
NEXT_PUBLIC_SECONDARY_COLOR=#f50057

# ==============================================
# ملاحظات مهمة - Important Notes
# ==============================================

# 1. قم بتغيير جميع كلمات المرور والمفاتيح السرية في بيئة الإنتاج
# 2. لا تشارك هذا الملف في نظام التحكم في الإصدارات
# 3. استخدم متغيرات بيئة آمنة في بيئة الإنتاج
# 4. تأكد من تشفير البيانات الحساسة
# 5. راجع إعدادات الأمان بانتظام
