import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Fade,
  LinearProgress,
} from '@mui/material';
import { styled } from '@mui/material/styles';

// تصميم الحاوية الرئيسية
const LoadingContainer = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.palette.background.default,
  zIndex: theme.zIndex.modal + 1,
}));

// تصميم شعار الشركة
const LogoContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  textAlign: 'center',
}));

// تصميم النص
const LoadingText = styled(Typography)(({ theme }) => ({
  marginTop: theme.spacing(2),
  color: theme.palette.text.secondary,
  fontWeight: 500,
}));

// تصميم شريط التقدم
const ProgressContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  maxWidth: 300,
  marginTop: theme.spacing(2),
}));

interface LoadingScreenProps {
  message?: string;
  showProgress?: boolean;
  progress?: number;
  variant?: 'circular' | 'linear' | 'logo';
  size?: number;
  thickness?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'جاري التحميل...',
  showProgress = false,
  progress = 0,
  variant = 'circular',
  size = 60,
  thickness = 4,
}) => {
  const [dots, setDots] = React.useState('');

  // تأثير النقاط المتحركة
  React.useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  const renderLoader = () => {
    switch (variant) {
      case 'linear':
        return (
          <ProgressContainer>
            <LinearProgress
              variant={showProgress ? 'determinate' : 'indeterminate'}
              value={progress}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 3,
                },
              }}
            />
            {showProgress && (
              <Typography
                variant="caption"
                sx={{ mt: 1, textAlign: 'center', display: 'block' }}
              >
                {Math.round(progress)}%
              </Typography>
            )}
          </ProgressContainer>
        );

      case 'logo':
        return (
          <LogoContainer>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': {
                    transform: 'scale(1)',
                    opacity: 1,
                  },
                  '50%': {
                    transform: 'scale(1.1)',
                    opacity: 0.8,
                  },
                  '100%': {
                    transform: 'scale(1)',
                    opacity: 1,
                  },
                },
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  color: 'white',
                  fontWeight: 'bold',
                }}
              >
                و
              </Typography>
            </Box>
          </LogoContainer>
        );

      default:
        return (
          <CircularProgress
            size={size}
            thickness={thickness}
            sx={{
              color: (theme) => theme.palette.primary.main,
            }}
          />
        );
    }
  };

  return (
    <Fade in timeout={300}>
      <LoadingContainer>
        {renderLoader()}
        
        <LoadingText variant="body1">
          {message}{dots}
        </LoadingText>

        {/* معلومات إضافية */}
        <Typography
          variant="caption"
          sx={{
            mt: 1,
            color: 'text.disabled',
            textAlign: 'center',
            maxWidth: 300,
          }}
        >
          نظام الوكالة التجارية المتكامل
        </Typography>
      </LoadingContainer>
    </Fade>
  );
};

// مكون تحميل مصغر للاستخدام داخل المكونات
export const MiniLoader: React.FC<{
  size?: number;
  message?: string;
}> = ({ size = 24, message }) => (
  <Box
    sx={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 1,
      p: 2,
    }}
  >
    <CircularProgress size={size} />
    {message && (
      <Typography variant="body2" color="text.secondary">
        {message}
      </Typography>
    )}
  </Box>
);

// مكون تحميل للأزرار
export const ButtonLoader: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  size?: number;
}> = ({ loading, children, size = 20 }) => (
  <Box sx={{ position: 'relative', display: 'inline-flex' }}>
    {children}
    {loading && (
      <CircularProgress
        size={size}
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          marginTop: `-${size / 2}px`,
          marginLeft: `-${size / 2}px`,
        }}
      />
    )}
  </Box>
);

// مكون تحميل للجداول
export const TableLoader: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: 200,
      flexDirection: 'column',
      gap: 2,
    }}
  >
    <CircularProgress />
    <Typography variant="body2" color="text.secondary">
      جاري تحميل البيانات...
    </Typography>
  </Box>
);

export default LoadingScreen;
