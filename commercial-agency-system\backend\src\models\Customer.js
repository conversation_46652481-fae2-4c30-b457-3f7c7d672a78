const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// نموذج العملاء
const Customer = sequelize.define('Customer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  customer_code: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    field: 'customer_code'
  },
  customer_type: {
    type: DataTypes.ENUM('individual', 'company'),
    allowNull: false,
    field: 'customer_type'
  },
  first_name: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'first_name',
    validate: {
      len: [2, 50]
    }
  },
  last_name: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'last_name',
    validate: {
      len: [2, 50]
    }
  },
  company_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'company_name',
    validate: {
      len: [2, 100]
    }
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^[\+]?[0-9\-\(\)\s]+$/
    }
  },
  mobile: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^[\+]?[0-9\-\(\)\s]+$/
    }
  },
  tax_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'tax_number'
  },
  commercial_register: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'commercial_register'
  },
  national_id: {
    type: DataTypes.STRING(20),
    allowNull: true,
    field: 'national_id'
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  country: {
    type: DataTypes.STRING(50),
    defaultValue: 'Saudi Arabia'
  },
  postal_code: {
    type: DataTypes.STRING(10),
    allowNull: true,
    field: 'postal_code'
  },
  credit_limit: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'credit_limit'
  },
  payment_terms: {
    type: DataTypes.INTEGER,
    defaultValue: 30,
    field: 'payment_terms'
  },
  customer_category: {
    type: DataTypes.ENUM('retail', 'wholesale', 'vip'),
    defaultValue: 'retail',
    field: 'customer_category'
  },
  discount_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0,
    field: 'discount_percentage'
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'SAR'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'updated_at'
  }
}, {
  tableName: 'customers',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['customer_code']
    },
    {
      fields: ['customer_type']
    },
    {
      fields: ['customer_category']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['created_by']
    }
  ],
  validate: {
    customerNameRequired() {
      if (this.customer_type === 'individual' && (!this.first_name || !this.last_name)) {
        throw new Error('الاسم الأول والأخير مطلوبان للعملاء الأفراد');
      }
      if (this.customer_type === 'company' && !this.company_name) {
        throw new Error('اسم الشركة مطلوب للعملاء الشركات');
      }
    }
  }
});

// الدوال الإضافية للنموذج
Customer.prototype.getDisplayName = function() {
  if (this.customer_type === 'individual') {
    return `${this.first_name} ${this.last_name}`;
  }
  return this.company_name;
};

Customer.prototype.getFullAddress = function() {
  const addressParts = [this.address, this.city, this.country].filter(Boolean);
  return addressParts.join(', ');
};

Customer.prototype.getCategoryName = function() {
  const categories = {
    retail: 'قطاعي',
    wholesale: 'جملة',
    vip: 'عميل مميز'
  };
  return categories[this.customer_category] || this.customer_category;
};

Customer.prototype.getTypeName = function() {
  const types = {
    individual: 'فرد',
    company: 'شركة'
  };
  return types[this.customer_type] || this.customer_type;
};

// الدوال الثابتة
Customer.generateCustomerCode = async function() {
  const lastCustomer = await this.findOne({
    order: [['id', 'DESC']],
    attributes: ['customer_code']
  });
  
  let nextNumber = 1;
  if (lastCustomer && lastCustomer.customer_code) {
    const lastNumber = parseInt(lastCustomer.customer_code.replace('CUS', ''));
    nextNumber = lastNumber + 1;
  }
  
  return `CUS${nextNumber.toString().padStart(6, '0')}`;
};

Customer.findByCode = async function(customerCode) {
  return await this.findOne({
    where: { customer_code: customerCode, is_active: true }
  });
};

Customer.findActiveCustomers = async function(options = {}) {
  return await this.findAll({
    where: { is_active: true },
    ...options
  });
};

Customer.createCustomer = async function(customerData) {
  try {
    if (!customerData.customer_code) {
      customerData.customer_code = await this.generateCustomerCode();
    }
    
    const customer = await this.create(customerData);
    return customer;
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      throw new Error('رمز العميل مستخدم بالفعل');
    }
    throw error;
  }
};

Customer.updateCustomer = async function(id, customerData) {
  const customer = await this.findByPk(id);
  if (!customer) {
    throw new Error('العميل غير موجود');
  }
  
  await customer.update(customerData);
  return customer;
};

Customer.deleteCustomer = async function(id) {
  const customer = await this.findByPk(id);
  if (!customer) {
    throw new Error('العميل غير موجود');
  }
  
  // التحقق من عدم وجود فواتير مرتبطة
  const SalesInvoice = require('./SalesInvoice');
  const invoicesCount = await SalesInvoice.count({ 
    where: { customer_id: id, status: { [sequelize.Op.ne]: 'cancelled' } }
  });
  
  if (invoicesCount > 0) {
    throw new Error('لا يمكن حذف العميل لوجود فواتير مرتبطة به');
  }
  
  // حذف منطقي
  await customer.update({ is_active: false });
  return customer;
};

Customer.searchCustomers = async function(searchTerm, options = {}) {
  const { Op } = require('sequelize');
  
  return await this.findAll({
    where: {
      is_active: true,
      [Op.or]: [
        { customer_code: { [Op.like]: `%${searchTerm}%` } },
        { first_name: { [Op.like]: `%${searchTerm}%` } },
        { last_name: { [Op.like]: `%${searchTerm}%` } },
        { company_name: { [Op.like]: `%${searchTerm}%` } },
        { email: { [Op.like]: `%${searchTerm}%` } },
        { phone: { [Op.like]: `%${searchTerm}%` } },
        { mobile: { [Op.like]: `%${searchTerm}%` } }
      ]
    },
    ...options
  });
};

Customer.getCustomerStats = async function() {
  const { Op } = require('sequelize');
  
  const totalCustomers = await this.count({ where: { is_active: true } });
  const individualCustomers = await this.count({ 
    where: { customer_type: 'individual', is_active: true } 
  });
  const companyCustomers = await this.count({ 
    where: { customer_type: 'company', is_active: true } 
  });
  const vipCustomers = await this.count({ 
    where: { customer_category: 'vip', is_active: true } 
  });
  
  return {
    total: totalCustomers,
    individual: individualCustomers,
    company: companyCustomers,
    vip: vipCustomers
  };
};

Customer.getCustomersByCategory = async function() {
  const results = await this.findAll({
    attributes: [
      'customer_category',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    where: { is_active: true },
    group: ['customer_category']
  });
  
  return results.map(result => ({
    category: result.customer_category,
    count: parseInt(result.dataValues.count)
  }));
};

Customer.getTopCustomers = async function(limit = 10) {
  // سيتم تنفيذ هذا عند إضافة نموذج فواتير البيع
  return [];
};

// تصدير النموذج
module.exports = Customer;
