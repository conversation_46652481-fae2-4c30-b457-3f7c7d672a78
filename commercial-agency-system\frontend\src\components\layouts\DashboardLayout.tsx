import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Collapse,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  Settings,
  Logout,
  Dashboard,
  People,
  Business,
  Inventory,
  ShoppingCart,
  Receipt,
  Payment,
  Assessment,
  ExpandLess,
  ExpandMore,
  Person,
  Store,
  LocalShipping,
  AccountBalance,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useRouter } from 'next/router';

import { useAuth } from '@/contexts/AuthContext';
import { useAppSelector, useAppDispatch } from '@/store';
import { selectSidebar, toggleSidebar, setSidebarOpen } from '@/store/slices/uiSlice';

// عرض الشريط الجانبي
const DRAWER_WIDTH = 280;

// تصميم الشريط الجانبي
const StyledDrawer = styled(Drawer)(({ theme }) => ({
  width: DRAWER_WIDTH,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: DRAWER_WIDTH,
    boxSizing: 'border-box',
    borderRight: `1px solid ${theme.palette.divider}`,
    background: theme.palette.background.paper,
  },
}));

// تصميم شريط التطبيق
const StyledAppBar = styled(AppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open?: boolean }>(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: DRAWER_WIDTH,
    width: `calc(100% - ${DRAWER_WIDTH}px)`,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

// تصميم المحتوى الرئيسي
const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })<{
  open?: boolean;
}>(({ theme, open }) => ({
  flexGrow: 1,
  padding: 0,
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: 0,
  ...(open && {
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    marginLeft: DRAWER_WIDTH,
  }),
}));

// عناصر القائمة
const menuItems = [
  {
    title: 'لوحة التحكم',
    icon: Dashboard,
    path: '/dashboard',
  },
  {
    title: 'إدارة العملاء',
    icon: People,
    children: [
      { title: 'قائمة العملاء', icon: Person, path: '/customers' },
      { title: 'إضافة عميل', icon: Person, path: '/customers/new' },
    ],
  },
  {
    title: 'إدارة الموردين',
    icon: Business,
    children: [
      { title: 'قائمة الموردين', icon: Store, path: '/suppliers' },
      { title: 'إضافة مورد', icon: Store, path: '/suppliers/new' },
    ],
  },
  {
    title: 'إدارة المنتجات',
    icon: Inventory,
    children: [
      { title: 'قائمة المنتجات', icon: Inventory, path: '/products' },
      { title: 'إضافة منتج', icon: Inventory, path: '/products/new' },
      { title: 'فئات المنتجات', icon: Inventory, path: '/products/categories' },
    ],
  },
  {
    title: 'إدارة المخزون',
    icon: LocalShipping,
    children: [
      { title: 'حالة المخزون', icon: LocalShipping, path: '/inventory' },
      { title: 'حركات المخزون', icon: LocalShipping, path: '/inventory/movements' },
      { title: 'تسوية المخزون', icon: LocalShipping, path: '/inventory/adjustments' },
    ],
  },
  {
    title: 'المبيعات',
    icon: ShoppingCart,
    children: [
      { title: 'فواتير البيع', icon: Receipt, path: '/sales/invoices' },
      { title: 'عروض الأسعار', icon: Receipt, path: '/sales/quotations' },
      { title: 'إنشاء فاتورة', icon: Receipt, path: '/sales/new' },
    ],
  },
  {
    title: 'المشتريات',
    icon: Receipt,
    children: [
      { title: 'فواتير الشراء', icon: Receipt, path: '/purchases/invoices' },
      { title: 'طلبات الشراء', icon: Receipt, path: '/purchases/orders' },
      { title: 'إنشاء طلب شراء', icon: Receipt, path: '/purchases/new' },
    ],
  },
  {
    title: 'المدفوعات',
    icon: Payment,
    children: [
      { title: 'المدفوعات الواردة', icon: AccountBalance, path: '/payments/received' },
      { title: 'المدفوعات الصادرة', icon: AccountBalance, path: '/payments/sent' },
      { title: 'إدارة الديون', icon: AccountBalance, path: '/payments/debts' },
    ],
  },
  {
    title: 'التقارير',
    icon: Assessment,
    children: [
      { title: 'تقارير المبيعات', icon: Assessment, path: '/reports/sales' },
      { title: 'تقارير المشتريات', icon: Assessment, path: '/reports/purchases' },
      { title: 'تقارير المخزون', icon: Assessment, path: '/reports/inventory' },
      { title: 'التقارير المالية', icon: Assessment, path: '/reports/financial' },
    ],
  },
];

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user, logout } = useAuth();
  const sidebar = useAppSelector(selectSidebar);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenus, setOpenMenus] = useState<{ [key: string]: boolean }>({});

  // فتح/إغلاق قائمة المستخدم
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // تبديل الشريط الجانبي
  const handleDrawerToggle = () => {
    dispatch(toggleSidebar());
  };

  // تبديل القوائم الفرعية
  const handleSubmenuToggle = (title: string) => {
    setOpenMenus(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  // التنقل إلى صفحة
  const handleNavigation = (path: string) => {
    router.push(path);
    if (isMobile) {
      dispatch(setSidebarOpen(false));
    }
  };

  // تسجيل الخروج
  const handleLogout = async () => {
    handleMenuClose();
    await logout();
  };

  // محتوى الشريط الجانبي
  const drawerContent = (
    <Box>
      {/* شعار التطبيق */}
      <Box sx={{ p: 2, textAlign: 'center', borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" component="div" fontWeight="bold" color="primary">
          الوكالة التجارية
        </Typography>
        <Typography variant="caption" color="text.secondary">
          نظام إدارة متكامل
        </Typography>
      </Box>

      {/* معلومات المستخدم */}
      <Box sx={{ p: 2, textAlign: 'center', borderBottom: 1, borderColor: 'divider' }}>
        <Avatar sx={{ width: 56, height: 56, mx: 'auto', mb: 1 }}>
          {user?.firstName?.charAt(0)}
        </Avatar>
        <Typography variant="subtitle2" fontWeight="medium">
          {user?.fullName}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {user?.role?.nameAr}
        </Typography>
      </Box>

      {/* قائمة التنقل */}
      <List sx={{ pt: 1 }}>
        {menuItems.map((item) => (
          <React.Fragment key={item.title}>
            <ListItemButton
              onClick={() => {
                if (item.children) {
                  handleSubmenuToggle(item.title);
                } else {
                  handleNavigation(item.path!);
                }
              }}
              selected={!item.children && router.pathname === item.path}
              sx={{
                minHeight: 48,
                px: 2.5,
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                <item.icon />
              </ListItemIcon>
              <ListItemText primary={item.title} />
              {item.children && (
                openMenus[item.title] ? <ExpandLess /> : <ExpandMore />
              )}
            </ListItemButton>

            {item.children && (
              <Collapse in={openMenus[item.title]} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {item.children.map((child) => (
                    <ListItemButton
                      key={child.title}
                      onClick={() => handleNavigation(child.path)}
                      selected={router.pathname === child.path}
                      sx={{ pl: 4 }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        <child.icon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={child.title} />
                    </ListItemButton>
                  ))}
                </List>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* شريط التطبيق */}
      <StyledAppBar position="fixed" open={sidebar.open && !isMobile}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerToggle}
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {/* يمكن إضافة عنوان الصفحة هنا */}
          </Typography>

          {/* أيقونات شريط التطبيق */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="الإشعارات">
              <IconButton color="inherit">
                <Badge badgeContent={4} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="الإعدادات">
              <IconButton color="inherit" onClick={() => router.push('/settings')}>
                <Settings />
              </IconButton>
            </Tooltip>

            <Tooltip title="حساب المستخدم">
              <IconButton onClick={handleMenuOpen} color="inherit">
                <Avatar sx={{ width: 32, height: 32 }}>
                  {user?.firstName?.charAt(0)}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>

          {/* قائمة المستخدم */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            onClick={handleMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem onClick={() => router.push('/profile')}>
              <Person sx={{ mr: 1 }} />
              الملف الشخصي
            </MenuItem>
            <MenuItem onClick={() => router.push('/settings')}>
              <Settings sx={{ mr: 1 }} />
              الإعدادات
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <Logout sx={{ mr: 1 }} />
              تسجيل الخروج
            </MenuItem>
          </Menu>
        </Toolbar>
      </StyledAppBar>

      {/* الشريط الجانبي */}
      <StyledDrawer
        variant={isMobile ? 'temporary' : 'persistent'}
        open={sidebar.open}
        onClose={() => dispatch(setSidebarOpen(false))}
        ModalProps={{
          keepMounted: true, // أداء أفضل على الأجهزة المحمولة
        }}
      >
        {drawerContent}
      </StyledDrawer>

      {/* المحتوى الرئيسي */}
      <Main open={sidebar.open && !isMobile}>
        <Toolbar />
        {children}
      </Main>
    </Box>
  );
};

export default DashboardLayout;
