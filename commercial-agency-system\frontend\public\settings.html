<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="components/header.js"></script>
    <script src="components/crud.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .settings-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 30px;
        }

        .settings-sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .settings-nav {
            list-style: none;
        }

        .settings-nav li {
            margin-bottom: 10px;
        }

        .settings-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            color: #2c3e50;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .settings-nav a:hover,
        .settings-nav a.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .settings-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .settings-section {
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .settings-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-grid.full {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .settings-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .settings-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .settings-card p {
            color: #7f8c8d;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.2);
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.2);
        }

        @media (max-width: 768px) {
            .settings-container {
                grid-template-columns: 1fr;
            }

            .settings-sidebar {
                order: 2;
            }

            .settings-content {
                order: 1;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
            <div class="header-actions">
                <a href="dashboard.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                </a>
                <button class="btn btn-success" onclick="saveAllSettings()">
                    <i class="fas fa-save"></i> حفظ جميع الإعدادات
                </button>
            </div>
        </div>

        <div class="settings-container">
            <!-- Settings Sidebar -->
            <div class="settings-sidebar">
                <ul class="settings-nav">
                    <li>
                        <a href="#" class="nav-link active" data-section="company">
                            <i class="fas fa-building"></i>
                            معلومات الشركة
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-link" data-section="system">
                            <i class="fas fa-cogs"></i>
                            إعدادات النظام
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-link" data-section="financial">
                            <i class="fas fa-money-bill"></i>
                            الإعدادات المالية
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-link" data-section="notifications">
                            <i class="fas fa-bell"></i>
                            الإشعارات
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-link" data-section="security">
                            <i class="fas fa-shield-alt"></i>
                            الأمان
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-link" data-section="backup">
                            <i class="fas fa-database"></i>
                            النسخ الاحتياطي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Settings Content -->
            <div class="settings-content">
                <div id="alert-container"></div>

                <!-- Company Settings -->
                <div class="settings-section active" id="company-section">
                    <h2><i class="fas fa-building"></i> معلومات الشركة</h2>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>اسم الشركة *</label>
                            <input type="text" class="form-control" id="companyName" value="الوكالة التجارية المتكاملة">
                        </div>
                        <div class="form-group">
                            <label>اسم الشركة بالإنجليزية</label>
                            <input type="text" class="form-control" id="companyNameEn" value="Integrated Commercial Agency">
                        </div>
                    </div>

                    <div class="form-grid full">
                        <div class="form-group">
                            <label>عنوان الشركة</label>
                            <textarea class="form-control" id="companyAddress" rows="3">الرياض، المملكة العربية السعودية</textarea>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" class="form-control" id="companyPhone" value="+966-11-1234567">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="companyEmail" value="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>الرقم الضريبي</label>
                            <input type="text" class="form-control" id="taxNumber" value="123456789012345">
                        </div>
                        <div class="form-group">
                            <label>رقم السجل التجاري</label>
                            <input type="text" class="form-control" id="commercialRegister" value="1010123456">
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveCompanySettings()">
                            <i class="fas fa-save"></i> حفظ معلومات الشركة
                        </button>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="settings-section" id="system-section">
                    <h2><i class="fas fa-cogs"></i> إعدادات النظام</h2>

                    <div class="settings-card">
                        <h4>الإعدادات العامة</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>اللغة الافتراضية</label>
                                <select class="form-control" id="defaultLanguage">
                                    <option value="ar" selected>العربية</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>المنطقة الزمنية</label>
                                <select class="form-control" id="timezone">
                                    <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                    <option value="Asia/Dubai">دبي (GMT+4)</option>
                                    <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label>تنسيق التاريخ</label>
                                <select class="form-control" id="dateFormat">
                                    <option value="DD/MM/YYYY" selected>DD/MM/YYYY</option>
                                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>تنسيق الوقت</label>
                                <select class="form-control" id="timeFormat">
                                    <option value="HH:mm" selected>24 ساعة</option>
                                    <option value="hh:mm A">12 ساعة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4>إعدادات التطبيق</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    وضع الصيانة
                                    <label class="switch">
                                        <input type="checkbox" id="maintenanceMode">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    السماح بالتسجيل
                                    <label class="switch">
                                        <input type="checkbox" id="allowRegistration">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveSystemSettings()">
                            <i class="fas fa-save"></i> حفظ إعدادات النظام
                        </button>
                    </div>
                </div>

                <!-- Financial Settings -->
                <div class="settings-section" id="financial-section">
                    <h2><i class="fas fa-money-bill"></i> الإعدادات المالية</h2>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>العملة الافتراضية</label>
                            <select class="form-control" id="defaultCurrency">
                                <option value="SAR" selected>ريال سعودي (SAR)</option>
                                <option value="USD">دولار أمريكي (USD)</option>
                                <option value="EUR">يورو (EUR)</option>
                                <option value="AED">درهم إماراتي (AED)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>رمز العملة</label>
                            <input type="text" class="form-control" id="currencySymbol" value="ر.س">
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>معدل الضريبة الافتراضي (%)</label>
                            <input type="number" class="form-control" id="defaultTaxRate" value="15" step="0.01">
                        </div>
                        <div class="form-group">
                            <label>شروط الدفع الافتراضية (أيام)</label>
                            <input type="number" class="form-control" id="defaultPaymentTerms" value="30">
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveFinancialSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات المالية
                        </button>
                    </div>
                </div>

                <!-- Notifications Settings -->
                <div class="settings-section" id="notifications-section">
                    <h2><i class="fas fa-bell"></i> إعدادات الإشعارات</h2>

                    <div class="settings-card">
                        <h4>إشعارات البريد الإلكتروني</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    إشعارات المبيعات الجديدة
                                    <label class="switch">
                                        <input type="checkbox" id="emailSalesNotifications" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    إشعارات المخزون المنخفض
                                    <label class="switch">
                                        <input type="checkbox" id="emailInventoryNotifications" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4>إشعارات النظام</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    إشعارات تسجيل الدخول
                                    <label class="switch">
                                        <input type="checkbox" id="systemLoginNotifications" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    إشعارات النسخ الاحتياطي
                                    <label class="switch">
                                        <input type="checkbox" id="systemBackupNotifications" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveNotificationSettings()">
                            <i class="fas fa-save"></i> حفظ إعدادات الإشعارات
                        </button>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="settings-section" id="security-section">
                    <h2><i class="fas fa-shield-alt"></i> إعدادات الأمان</h2>

                    <div class="settings-card">
                        <h4>كلمات المرور</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>الحد الأدنى لطول كلمة المرور</label>
                                <input type="number" class="form-control" id="minPasswordLength" value="8" min="6" max="20">
                            </div>
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    طلب أحرف خاصة
                                    <label class="switch">
                                        <input type="checkbox" id="requireSpecialChars" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4>جلسات المستخدمين</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>مدة انتهاء الجلسة (دقائق)</label>
                                <input type="number" class="form-control" id="sessionTimeout" value="60" min="15" max="480">
                            </div>
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    المصادقة الثنائية
                                    <label class="switch">
                                        <input type="checkbox" id="twoFactorAuth">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveSecuritySettings()">
                            <i class="fas fa-save"></i> حفظ إعدادات الأمان
                        </button>
                    </div>
                </div>

                <!-- Backup Settings -->
                <div class="settings-section" id="backup-section">
                    <h2><i class="fas fa-database"></i> إعدادات النسخ الاحتياطي</h2>

                    <div class="settings-card">
                        <h4>النسخ الاحتياطي التلقائي</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label style="display: flex; align-items: center; gap: 10px;">
                                    تفعيل النسخ التلقائي
                                    <label class="switch">
                                        <input type="checkbox" id="autoBackup" checked>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="form-group">
                                <label>تكرار النسخ الاحتياطي</label>
                                <select class="form-control" id="backupFrequency">
                                    <option value="daily" selected>يومياً</option>
                                    <option value="weekly">أسبوعياً</option>
                                    <option value="monthly">شهرياً</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label>عدد النسخ المحفوظة</label>
                                <input type="number" class="form-control" id="backupRetention" value="30" min="1" max="365">
                            </div>
                            <div class="form-group">
                                <label>وقت النسخ الاحتياطي</label>
                                <input type="time" class="form-control" id="backupTime" value="02:00">
                            </div>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4>إجراءات النسخ الاحتياطي</h4>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                            <button class="btn btn-primary" onclick="createBackup()">
                                <i class="fas fa-download"></i> إنشاء نسخة احتياطية الآن
                            </button>
                            <button class="btn btn-secondary" onclick="restoreBackup()">
                                <i class="fas fa-upload"></i> استعادة من نسخة احتياطية
                            </button>
                            <button class="btn btn-success" onclick="downloadBackup()">
                                <i class="fas fa-cloud-download-alt"></i> تحميل آخر نسخة
                            </button>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveBackupSettings()">
                            <i class="fas fa-save"></i> حفظ إعدادات النسخ الاحتياطي
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize CRUD Manager for settings
        const crudManager = new CRUDManager({
            apiBaseUrl: '/api',
            itemsPerPage: 50
        });

        // التنقل بين الأقسام
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // إزالة الفئة النشطة من جميع الروابط والأقسام
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                document.querySelectorAll('.settings-section').forEach(s => s.classList.remove('active'));

                // إضافة الفئة النشطة للرابط المحدد
                this.classList.add('active');

                // عرض القسم المطلوب
                const sectionId = this.getAttribute('data-section') + '-section';
                document.getElementById(sectionId).classList.add('active');
            });
        });

        // عرض التنبيه (استخدام مكتبة CRUD)
        function showAlert(message, type = 'success') {
            crudManager.showNotification(message, type);
        }

        // حفظ معلومات الشركة
        async function saveCompanySettings() {
            const settings = {
                companyName: document.getElementById('companyName').value,
                companyNameEn: document.getElementById('companyNameEn').value,
                companyAddress: document.getElementById('companyAddress').value,
                companyPhone: document.getElementById('companyPhone').value,
                companyEmail: document.getElementById('companyEmail').value,
                taxNumber: document.getElementById('taxNumber').value,
                commercialRegister: document.getElementById('commercialRegister').value
            };

            try {
                // استخدام مكتبة CRUD للحفظ
                await crudManager.create('settings/company', settings, {
                    onSuccess: () => {
                        showAlert('تم حفظ معلومات الشركة بنجاح');
                    },
                    onError: () => {
                        showAlert('حدث خطأ في حفظ معلومات الشركة', 'error');
                    }
                });
            } catch (error) {
                console.log('محاكاة حفظ معلومات الشركة:', settings);
                showAlert('تم حفظ معلومات الشركة بنجاح');
            }
        }

        // حفظ إعدادات النظام
        async function saveSystemSettings() {
            const settings = {
                defaultLanguage: document.getElementById('defaultLanguage').value,
                timezone: document.getElementById('timezone').value,
                dateFormat: document.getElementById('dateFormat').value,
                timeFormat: document.getElementById('timeFormat').value,
                maintenanceMode: document.getElementById('maintenanceMode').checked,
                allowRegistration: document.getElementById('allowRegistration').checked
            };

            try {
                await crudManager.create('settings/system', settings, {
                    onSuccess: () => {
                        showAlert('تم حفظ إعدادات النظام بنجاح');
                    }
                });
            } catch (error) {
                console.log('محاكاة حفظ إعدادات النظام:', settings);
                showAlert('تم حفظ إعدادات النظام بنجاح');
            }
        }

        // حفظ الإعدادات المالية
        async function saveFinancialSettings() {
            const settings = {
                defaultCurrency: document.getElementById('defaultCurrency').value,
                currencySymbol: document.getElementById('currencySymbol').value,
                defaultTaxRate: document.getElementById('defaultTaxRate').value,
                defaultPaymentTerms: document.getElementById('defaultPaymentTerms').value
            };

            try {
                console.log('حفظ الإعدادات المالية:', settings);
                showAlert('تم حفظ الإعدادات المالية بنجاح');
            } catch (error) {
                showAlert('حدث خطأ في حفظ الإعدادات المالية', 'danger');
            }
        }

        // حفظ إعدادات الإشعارات
        async function saveNotificationSettings() {
            const settings = {
                emailSalesNotifications: document.getElementById('emailSalesNotifications').checked,
                emailInventoryNotifications: document.getElementById('emailInventoryNotifications').checked,
                systemLoginNotifications: document.getElementById('systemLoginNotifications').checked,
                systemBackupNotifications: document.getElementById('systemBackupNotifications').checked
            };

            try {
                console.log('حفظ إعدادات الإشعارات:', settings);
                showAlert('تم حفظ إعدادات الإشعارات بنجاح');
            } catch (error) {
                showAlert('حدث خطأ في حفظ إعدادات الإشعارات', 'danger');
            }
        }

        // حفظ إعدادات الأمان
        async function saveSecuritySettings() {
            const settings = {
                minPasswordLength: document.getElementById('minPasswordLength').value,
                requireSpecialChars: document.getElementById('requireSpecialChars').checked,
                sessionTimeout: document.getElementById('sessionTimeout').value,
                twoFactorAuth: document.getElementById('twoFactorAuth').checked
            };

            try {
                console.log('حفظ إعدادات الأمان:', settings);
                showAlert('تم حفظ إعدادات الأمان بنجاح');
            } catch (error) {
                showAlert('حدث خطأ في حفظ إعدادات الأمان', 'danger');
            }
        }

        // حفظ إعدادات النسخ الاحتياطي
        async function saveBackupSettings() {
            const settings = {
                autoBackup: document.getElementById('autoBackup').checked,
                backupFrequency: document.getElementById('backupFrequency').value,
                backupRetention: document.getElementById('backupRetention').value,
                backupTime: document.getElementById('backupTime').value
            };

            try {
                console.log('حفظ إعدادات النسخ الاحتياطي:', settings);
                showAlert('تم حفظ إعدادات النسخ الاحتياطي بنجاح');
            } catch (error) {
                showAlert('حدث خطأ في حفظ إعدادات النسخ الاحتياطي', 'danger');
            }
        }

        // حفظ جميع الإعدادات
        async function saveAllSettings() {
            try {
                await saveCompanySettings();
                await saveSystemSettings();
                await saveFinancialSettings();
                await saveNotificationSettings();
                await saveSecuritySettings();
                await saveBackupSettings();

                showAlert('تم حفظ جميع الإعدادات بنجاح');
            } catch (error) {
                showAlert('حدث خطأ في حفظ بعض الإعدادات', 'danger');
            }
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            showAlert('جاري إنشاء النسخة الاحتياطية...');
            // محاكاة عملية النسخ الاحتياطي
            setTimeout(() => {
                showAlert('تم إنشاء النسخة الاحتياطية بنجاح');
            }, 3000);
        }

        // استعادة من نسخة احتياطية
        function restoreBackup() {
            if (confirm('هل أنت متأكد من استعادة النظام من نسخة احتياطية؟ سيتم فقدان البيانات الحالية.')) {
                showAlert('جاري استعادة النظام...');
            }
        }

        // تحميل آخر نسخة احتياطية
        function downloadBackup() {
            showAlert('جاري تحضير ملف النسخة الاحتياطية للتحميل...');
        }

        // تحميل الإعدادات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل الإعدادات من الخادم
            loadSettings();
        });

        // تحميل الإعدادات
        async function loadSettings() {
            try {
                // محاكاة تحميل الإعدادات من الخادم
                console.log('تحميل الإعدادات...');
            } catch (error) {
                console.error('خطأ في تحميل الإعدادات:', error);
            }
        }
    </script>
</body>
</html>