const { Sequelize } = require('sequelize');
const dotenv = require('dotenv');

dotenv.config();

// إعداد قاعدة البيانات MySQL
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    dialect: process.env.DB_DIALECT || 'mysql',
    timezone: process.env.TIMEZONE || '+03:00',
    
    // إعدادات الاتصال
    pool: {
      max: parseInt(process.env.DB_POOL_MAX) || 20,
      min: parseInt(process.env.DB_POOL_MIN) || 5,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE) || 10000,
    },
    
    // إعدادات الأداء
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    benchmark: process.env.NODE_ENV === 'development',
    
    // إعدادات الأمان
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      supportBigNumbers: true,
      bigNumberStrings: true,
      dateStrings: true,
      typeCast: true,
      ssl: process.env.NODE_ENV === 'production' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    },
    
    // إعدادات إضافية
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      underscored: false,
      freezeTableName: true,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    },
    
    // إعدادات الاستعلامات
    query: {
      raw: false,
      nest: false
    },
    
    // إعدادات المزامنة
    sync: {
      force: false,
      alter: process.env.NODE_ENV === 'development'
    }
  }
);

// اختبار الاتصال
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    console.log(`📊 قاعدة البيانات: ${process.env.DB_NAME}`);
    console.log(`🖥️  الخادم: ${process.env.DB_HOST}:${process.env.DB_PORT}`);
  } catch (error) {
    console.error('❌ فشل في الاتصال بقاعدة البيانات:', error.message);
    process.exit(1);
  }
};

// إعداد الاستعلامات المخصصة
const executeQuery = async (query, replacements = {}) => {
  try {
    const [results, metadata] = await sequelize.query(query, {
      replacements,
      type: Sequelize.QueryTypes.SELECT
    });
    return results;
  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error.message);
    throw error;
  }
};

// إعداد المعاملات
const createTransaction = async () => {
  return await sequelize.transaction();
};

// إعداد النسخ الاحتياطي
const createBackup = async () => {
  const backupName = `backup_${new Date().toISOString().replace(/[:.]/g, '-')}`;
  const query = `
    SELECT CONCAT('mysqldump -u ${process.env.DB_USER} -p${process.env.DB_PASSWORD} ${process.env.DB_NAME} > ${backupName}.sql') as backup_command
  `;
  
  try {
    const result = await executeQuery(query);
    console.log('أمر النسخ الاحتياطي:', result[0].backup_command);
    return result[0].backup_command;
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error.message);
    throw error;
  }
};

// إعداد الفهارس
const createIndexes = async () => {
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_products_search ON products(name, name_ar, product_code)',
    'CREATE INDEX IF NOT EXISTS idx_customers_search ON customers(first_name, last_name, company_name)',
    'CREATE INDEX IF NOT EXISTS idx_suppliers_search ON suppliers(company_name, supplier_code)',
    'CREATE INDEX IF NOT EXISTS idx_invoices_date ON sales_invoices(invoice_date, status)',
    'CREATE INDEX IF NOT EXISTS idx_purchase_date ON purchase_invoices(invoice_date, status)',
    'CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date, payment_type)'
  ];
  
  try {
    for (const index of indexes) {
      await sequelize.query(index);
    }
    console.log('✅ تم إنشاء الفهارس بنجاح');
  } catch (error) {
    console.error('❌ خطأ في إنشاء الفهارس:', error.message);
  }
};

// إعداد الإجراءات المخزنة
const createStoredProcedures = async () => {
  const procedures = [
    `
    CREATE PROCEDURE IF NOT EXISTS CalculateCustomerBalance(IN customer_id INT)
    BEGIN
        DECLARE total_invoices DECIMAL(15,2) DEFAULT 0;
        DECLARE total_payments DECIMAL(15,2) DEFAULT 0;
        DECLARE current_balance DECIMAL(15,2) DEFAULT 0;
        
        SELECT COALESCE(SUM(total_amount), 0) INTO total_invoices
        FROM sales_invoices 
        WHERE customer_id = customer_id AND status != 'cancelled';
        
        SELECT COALESCE(SUM(amount), 0) INTO total_payments
        FROM payments 
        WHERE customer_id = customer_id AND payment_type = 'received' AND status = 'completed';
        
        SET current_balance = total_invoices - total_payments;
        
        INSERT INTO customer_balances (customer_id, balance, last_transaction_date)
        VALUES (customer_id, current_balance, CURDATE())
        ON DUPLICATE KEY UPDATE 
            balance = current_balance,
            last_transaction_date = CURDATE();
    END
    `,
    `
    CREATE PROCEDURE IF NOT EXISTS CalculateSupplierBalance(IN supplier_id INT)
    BEGIN
        DECLARE total_invoices DECIMAL(15,2) DEFAULT 0;
        DECLARE total_payments DECIMAL(15,2) DEFAULT 0;
        DECLARE current_balance DECIMAL(15,2) DEFAULT 0;
        
        SELECT COALESCE(SUM(total_amount), 0) INTO total_invoices
        FROM purchase_invoices 
        WHERE supplier_id = supplier_id AND status != 'cancelled';
        
        SELECT COALESCE(SUM(amount), 0) INTO total_payments
        FROM payments 
        WHERE supplier_id = supplier_id AND payment_type = 'paid' AND status = 'completed';
        
        SET current_balance = total_invoices - total_payments;
        
        INSERT INTO supplier_balances (supplier_id, balance, last_transaction_date)
        VALUES (supplier_id, current_balance, CURDATE())
        ON DUPLICATE KEY UPDATE 
            balance = current_balance,
            last_transaction_date = CURDATE();
    END
    `
  ];
  
  try {
    for (const procedure of procedures) {
      await sequelize.query(procedure);
    }
    console.log('✅ تم إنشاء الإجراءات المخزنة بنجاح');
  } catch (error) {
    console.error('❌ خطأ في إنشاء الإجراءات المخزنة:', error.message);
  }
};

// إعداد المشغلات
const createTriggers = async () => {
  // سيتم إضافة المشغلات هنا إذا لزم الأمر
  console.log('✅ تم إعداد المشغلات');
};

// تهيئة قاعدة البيانات
const initializeDatabase = async () => {
  try {
    await testConnection();
    await createIndexes();
    await createStoredProcedures();
    await createTriggers();
    console.log('🎉 تم تهيئة قاعدة البيانات بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error.message);
    throw error;
  }
};

// إغلاق الاتصال
const closeConnection = async () => {
  try {
    await sequelize.close();
    console.log('✅ تم إغلاق الاتصال بقاعدة البيانات');
  } catch (error) {
    console.error('❌ خطأ في إغلاق الاتصال:', error.message);
  }
};

module.exports = {
  sequelize,
  testConnection,
  executeQuery,
  createTransaction,
  createBackup,
  initializeDatabase,
  closeConnection,
  Sequelize
};
