import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardA<PERSON>,
  Alert,
  Collapse,
  IconButton,
} from '@mui/material';
import {
  ErrorOutline,
  Refresh,
  Home,
  ExpandMore,
  ExpandLess,
  BugReport,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { FallbackProps } from 'react-error-boundary';

// تصميم الحاوية الرئيسية
const ErrorContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '100vh',
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
}));

// تصميم أيقونة الخطأ
const ErrorIcon = styled(ErrorOutline)(({ theme }) => ({
  fontSize: 80,
  color: theme.palette.error.main,
  marginBottom: theme.spacing(2),
}));

// تصميم بطاقة الخطأ
const ErrorCard = styled(Card)(({ theme }) => ({
  maxWidth: 600,
  width: '100%',
  textAlign: 'center',
  boxShadow: theme.shadows[3],
}));

interface ErrorFallbackProps extends FallbackProps {
  title?: string;
  subtitle?: string;
  showDetails?: boolean;
  showReportButton?: boolean;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  title = 'حدث خطأ غير متوقع',
  subtitle = 'نعتذر عن هذا الإزعاج. يرجى المحاولة مرة أخرى.',
  showDetails = true,
  showReportButton = true,
}) => {
  const [showErrorDetails, setShowErrorDetails] = React.useState(false);
  const [reportSent, setReportSent] = React.useState(false);

  // دالة إرسال تقرير الخطأ
  const handleReportError = async () => {
    try {
      // هنا يمكن إرسال تقرير الخطأ إلى الخادم
      const errorReport = {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      console.log('تقرير الخطأ:', errorReport);
      
      // محاكاة إرسال التقرير
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setReportSent(true);
    } catch (reportError) {
      console.error('فشل في إرسال تقرير الخطأ:', reportError);
    }
  };

  // دالة إعادة تحميل الصفحة
  const handleReload = () => {
    window.location.reload();
  };

  // دالة العودة للصفحة الرئيسية
  const handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  return (
    <ErrorContainer>
      <ErrorCard>
        <CardContent sx={{ p: 4 }}>
          <ErrorIcon />
          
          <Typography variant="h4" component="h1" gutterBottom>
            {title}
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph>
            {subtitle}
          </Typography>

          {/* رسالة الخطأ المبسطة */}
          <Alert 
            severity="error" 
            sx={{ mt: 2, mb: 2, textAlign: 'right' }}
          >
            <Typography variant="body2">
              {error.message || 'خطأ غير محدد'}
            </Typography>
          </Alert>

          {/* تفاصيل الخطأ التقنية */}
          {showDetails && (
            <>
              <Button
                startIcon={showErrorDetails ? <ExpandLess /> : <ExpandMore />}
                onClick={() => setShowErrorDetails(!showErrorDetails)}
                size="small"
                sx={{ mb: 1 }}
              >
                {showErrorDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل التقنية'}
              </Button>

              <Collapse in={showErrorDetails}>
                <Alert 
                  severity="warning" 
                  sx={{ 
                    mt: 1, 
                    textAlign: 'left',
                    '& .MuiAlert-message': {
                      width: '100%',
                    }
                  }}
                >
                  <Typography 
                    variant="caption" 
                    component="pre"
                    sx={{ 
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                      fontSize: '0.75rem',
                      fontFamily: 'monospace',
                    }}
                  >
                    {error.stack}
                  </Typography>
                </Alert>
              </Collapse>
            </>
          )}

          {/* معلومات إضافية */}
          <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              إذا استمر هذا الخطأ، يرجى التواصل مع الدعم التقني
            </Typography>
          </Box>
        </CardContent>

        <CardActions sx={{ justifyContent: 'center', p: 3, pt: 0 }}>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={resetErrorBoundary}
            sx={{ mr: 1 }}
          >
            إعادة المحاولة
          </Button>

          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleReload}
            sx={{ mr: 1 }}
          >
            إعادة تحميل الصفحة
          </Button>

          <Button
            variant="outlined"
            startIcon={<Home />}
            onClick={handleGoHome}
            sx={{ mr: 1 }}
          >
            الصفحة الرئيسية
          </Button>

          {showReportButton && (
            <Button
              variant="text"
              startIcon={<BugReport />}
              onClick={handleReportError}
              disabled={reportSent}
              size="small"
            >
              {reportSent ? 'تم الإرسال' : 'إبلاغ عن الخطأ'}
            </Button>
          )}
        </CardActions>
      </ErrorCard>

      {/* معلومات النظام */}
      <Typography 
        variant="caption" 
        color="text.disabled" 
        sx={{ mt: 2, textAlign: 'center' }}
      >
        نظام الوكالة التجارية المتكامل - الإصدار 1.0.0
        <br />
        {new Date().toLocaleString('ar-SA')}
      </Typography>
    </ErrorContainer>
  );
};

// مكون خطأ مصغر للاستخدام داخل المكونات
export const InlineError: React.FC<{
  error: Error;
  onRetry?: () => void;
  compact?: boolean;
}> = ({ error, onRetry, compact = false }) => (
  <Alert 
    severity="error"
    action={
      onRetry && (
        <IconButton
          aria-label="إعادة المحاولة"
          color="inherit"
          size="small"
          onClick={onRetry}
        >
          <Refresh fontSize="inherit" />
        </IconButton>
      )
    }
    sx={{ m: compact ? 1 : 2 }}
  >
    <Typography variant={compact ? "caption" : "body2"}>
      {error.message || 'حدث خطأ'}
    </Typography>
  </Alert>
);

// مكون خطأ للصفحات
export const PageError: React.FC<{
  title?: string;
  message?: string;
  onRetry?: () => void;
}> = ({ 
  title = 'خطأ في تحميل الصفحة',
  message = 'حدث خطأ أثناء تحميل هذه الصفحة',
  onRetry 
}) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 400,
      textAlign: 'center',
      p: 3,
    }}
  >
    <ErrorOutline sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
    <Typography variant="h5" gutterBottom>
      {title}
    </Typography>
    <Typography variant="body1" color="text.secondary" paragraph>
      {message}
    </Typography>
    {onRetry && (
      <Button
        variant="contained"
        startIcon={<Refresh />}
        onClick={onRetry}
      >
        إعادة المحاولة
      </Button>
    )}
  </Box>
);

export default ErrorFallback;
