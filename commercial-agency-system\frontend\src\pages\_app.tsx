import React from 'react';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { CacheProvider, EmotionCache } from '@emotion/react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';
import { ErrorBoundary } from 'react-error-boundary';

// الاستيرادات المحلية
import { store, persistor } from '@/store';
import theme from '@/styles/theme';
import createEmotionCache from '@/utils/createEmotionCache';
import { AuthProvider } from '@/contexts/AuthContext';
import { SettingsProvider } from '@/contexts/SettingsContext';
import LoadingScreen from '@/components/common/LoadingScreen';
import ErrorFallback from '@/components/common/ErrorFallback';
import ProgressBar from '@/components/common/ProgressBar';

// إنشاء cache للـ emotion
const clientSideEmotionCache = createEmotionCache();

// إنشاء QueryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 دقائق
      cacheTime: 10 * 60 * 1000, // 10 دقائق
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

interface MyAppProps extends AppProps {
  emotionCache?: EmotionCache;
}

function MyApp(props: MyAppProps) {
  const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;

  React.useEffect(() => {
    // إزالة CSS المولد من الخادم
    const jssStyles = document.querySelector('#jss-server-side');
    if (jssStyles) {
      jssStyles.parentElement?.removeChild(jssStyles);
    }
  }, []);

  return (
    <CacheProvider value={emotionCache}>
      <Head>
        <title>نظام الوكالة التجارية المتكامل</title>
        <meta name="viewport" content="initial-scale=1, width=device-width" />
        <meta name="description" content="نظام إدارة شامل للوكالات التجارية" />
        <meta name="keywords" content="وكالة تجارية, إدارة المخزون, المبيعات, المشتريات" />
        <meta name="author" content="Commercial Agency System Team" />
        
        {/* أيقونات التطبيق */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* خطوط Google */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link
          href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
        
        {/* Meta tags للشبكات الاجتماعية */}
        <meta property="og:title" content="نظام الوكالة التجارية المتكامل" />
        <meta property="og:description" content="نظام إدارة شامل للوكالات التجارية" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://commercial-agency.com" />
        <meta property="og:image" content="/og-image.png" />
        
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="نظام الوكالة التجارية المتكامل" />
        <meta name="twitter:description" content="نظام إدارة شامل للوكالات التجارية" />
        <meta name="twitter:image" content="/twitter-image.png" />
      </Head>

      <HelmetProvider>
        <ErrorBoundary
          FallbackComponent={ErrorFallback}
          onError={(error, errorInfo) => {
            console.error('خطأ في التطبيق:', error, errorInfo);
            // يمكن إرسال الخطأ إلى خدمة مراقبة الأخطاء هنا
          }}
        >
          <Provider store={store}>
            <PersistGate loading={<LoadingScreen />} persistor={persistor}>
              <QueryClientProvider client={queryClient}>
                <ThemeProvider theme={theme}>
                  <CssBaseline />
                  <SettingsProvider>
                    <AuthProvider>
                      <ProgressBar />
                      <Component {...pageProps} />
                      <Toaster
                        position="top-center"
                        reverseOrder={false}
                        gutter={8}
                        containerClassName=""
                        containerStyle={{}}
                        toastOptions={{
                          // إعدادات افتراضية للإشعارات
                          className: '',
                          duration: 4000,
                          style: {
                            background: '#363636',
                            color: '#fff',
                            fontFamily: 'Cairo, sans-serif',
                            fontSize: '14px',
                            borderRadius: '8px',
                            padding: '12px 16px',
                          },
                          // إعدادات مخصصة لكل نوع
                          success: {
                            duration: 3000,
                            style: {
                              background: '#4caf50',
                            },
                            iconTheme: {
                              primary: '#fff',
                              secondary: '#4caf50',
                            },
                          },
                          error: {
                            duration: 5000,
                            style: {
                              background: '#f44336',
                            },
                            iconTheme: {
                              primary: '#fff',
                              secondary: '#f44336',
                            },
                          },
                          loading: {
                            duration: Infinity,
                          },
                        }}
                      />
                    </AuthProvider>
                  </SettingsProvider>
                </ThemeProvider>
                {process.env.NODE_ENV === 'development' && (
                  <ReactQueryDevtools initialIsOpen={false} />
                )}
              </QueryClientProvider>
            </PersistGate>
          </Provider>
        </ErrorBoundary>
      </HelmetProvider>
    </CacheProvider>
  );
}

export default MyApp;
