import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authAPI } from '@/services/api/authAPI';

// تعريف أنواع البيانات
export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  avatar?: string;
  role: {
    id: number;
    name: string;
    nameAr: string;
    permissions: string[];
  };
  lastLogin?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  loginAttempts: number;
  isLocked: boolean;
  lockUntil: string | null;
}

// الحالة الأولية
const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginAttempts: 0,
  isLocked: false,
  lockUntil: null,
};

// العمليات غير المتزامنة (Async Thunks)

// تسجيل الدخول
export const login = createAsyncThunk(
  'auth/login',
  async (
    credentials: { username: string; password: string; rememberMe?: boolean },
    { rejectWithValue }
  ) => {
    try {
      const response = await authAPI.login(credentials);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'فشل في تسجيل الدخول'
      );
    }
  }
);

// تسجيل الخروج
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authAPI.logout();
      return true;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'فشل في تسجيل الخروج'
      );
    }
  }
);

// تحديث الرمز المميز
export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.refreshToken();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'فشل في تحديث الرمز المميز'
      );
    }
  }
);

// التحقق من حالة المصادقة
export const checkAuth = createAsyncThunk(
  'auth/checkAuth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.checkAuth();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'فشل في التحقق من المصادقة'
      );
    }
  }
);

// تغيير كلمة المرور
export const changePassword = createAsyncThunk(
  'auth/changePassword',
  async (
    passwords: {
      currentPassword: string;
      newPassword: string;
      confirmPassword: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await authAPI.changePassword(passwords);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'فشل في تغيير كلمة المرور'
      );
    }
  }
);

// طلب إعادة تعيين كلمة المرور
export const forgotPassword = createAsyncThunk(
  'auth/forgotPassword',
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await authAPI.forgotPassword(email);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'فشل في إرسال رابط إعادة التعيين'
      );
    }
  }
);

// إعادة تعيين كلمة المرور
export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async (
    data: { token: string; newPassword: string; confirmPassword: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await authAPI.resetPassword(data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'فشل في إعادة تعيين كلمة المرور'
      );
    }
  }
);

// إنشاء الـ slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // مسح الخطأ
    clearError: (state) => {
      state.error = null;
    },
    
    // تعيين الرمز المميز
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
    },
    
    // تعيين المستخدم
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    
    // مسح بيانات المصادقة
    clearAuth: (state) => {
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
      state.error = null;
      state.loginAttempts = 0;
      state.isLocked = false;
      state.lockUntil = null;
    },
    
    // زيادة محاولات تسجيل الدخول
    incrementLoginAttempts: (state) => {
      state.loginAttempts += 1;
      if (state.loginAttempts >= 5) {
        state.isLocked = true;
        state.lockUntil = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30 دقيقة
      }
    },
    
    // إعادة تعيين محاولات تسجيل الدخول
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
      state.isLocked = false;
      state.lockUntil = null;
    },
    
    // تحديث بيانات المستخدم
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    // تسجيل الدخول
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.accessToken;
        state.isAuthenticated = true;
        state.error = null;
        state.loginAttempts = 0;
        state.isLocked = false;
        state.lockUntil = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.loginAttempts += 1;
        if (state.loginAttempts >= 5) {
          state.isLocked = true;
          state.lockUntil = new Date(Date.now() + 30 * 60 * 1000).toISOString();
        }
      });

    // تسجيل الخروج
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
        state.isLoading = false;
        state.error = null;
        state.loginAttempts = 0;
        state.isLocked = false;
        state.lockUntil = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // تحديث الرمز المميز
    builder
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.token = action.payload.accessToken;
        state.error = null;
      })
      .addCase(refreshToken.rejected, (state) => {
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
      });

    // التحقق من المصادقة
    builder
      .addCase(checkAuth.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(checkAuth.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
      });

    // تغيير كلمة المرور
    builder
      .addCase(changePassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// تصدير الإجراءات
export const {
  clearError,
  setToken,
  setUser,
  clearAuth,
  incrementLoginAttempts,
  resetLoginAttempts,
  updateUser,
} = authSlice.actions;

// Selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) => state.auth.isLoading;
export const selectError = (state: { auth: AuthState }) => state.auth.error;
export const selectUserPermissions = (state: { auth: AuthState }) => 
  state.auth.user?.role?.permissions || [];

// دالة للتحقق من الصلاحية
export const hasPermission = (permissions: string[], requiredPermission: string) => {
  return permissions.includes('admin.all') || permissions.includes(requiredPermission);
};

// تصدير المقلل
export default authSlice.reducer;
