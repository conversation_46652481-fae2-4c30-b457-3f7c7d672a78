import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { NextPage } from 'next';
import LoadingScreen from '@/components/common/LoadingScreen';

const HomePage: NextPage = () => {
  const router = useRouter();

  useEffect(() => {
    // إعادة توجيه إلى لوحة التحكم
    router.replace('/dashboard');
  }, [router]);

  return <LoadingScreen message="جاري التحميل..." />;
};

export default HomePage;
