<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="components/header.js"></script>
    <script src="components/crud.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .search-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .suppliers-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #2c3e50;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .status-inactive {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .modal-header h3 {
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row.full {
            grid-template-columns: 1fr;
        }

        @media (max-width: 768px) {
            .search-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-truck"></i> إدارة الموردين</h1>
            <div class="header-actions">
                <a href="dashboard.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                </a>
                <button class="btn btn-primary" onclick="openAddModal()">
                    <i class="fas fa-plus"></i> إضافة مورد جديد
                </button>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-grid">
                <div class="form-group">
                    <label>البحث بالاسم</label>
                    <input type="text" class="form-control" id="searchName" placeholder="ادخل اسم المورد">
                </div>
                <div class="form-group">
                    <label>البحث بالهاتف</label>
                    <input type="text" class="form-control" id="searchPhone" placeholder="ادخل رقم الهاتف">
                </div>
                <div class="form-group">
                    <label>الحالة</label>
                    <select class="form-control" id="searchStatus">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="searchSuppliers()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Suppliers Table -->
        <div class="suppliers-table">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...
            </div>
            
            <div id="suppliersContent" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم المورد</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>العنوان</th>
                            <th>نوع المورد</th>
                            <th>الحالة</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="suppliersTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <!-- أزرار التصفح -->
                </div>
            </div>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-truck"></i>
                <h3>لا توجد موردين</h3>
                <p>لم يتم العثور على أي موردين. ابدأ بإضافة مورد جديد.</p>
            </div>
        </div>
    </div>

    <!-- Add/Edit Supplier Modal -->
    <div id="supplierModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة مورد جديد</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="supplierForm">
                <input type="hidden" id="supplierId">
                
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم المورد *</label>
                        <input type="text" class="form-control" id="supplierName" required>
                    </div>
                    <div class="form-group">
                        <label>اسم جهة الاتصال</label>
                        <input type="text" class="form-control" id="contactName">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>رقم الهاتف *</label>
                        <input type="tel" class="form-control" id="phone" required>
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email">
                    </div>
                </div>
                
                <div class="form-row full">
                    <div class="form-group">
                        <label>العنوان</label>
                        <textarea class="form-control" id="address" rows="3"></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>المدينة</label>
                        <input type="text" class="form-control" id="city">
                    </div>
                    <div class="form-group">
                        <label>الرمز البريدي</label>
                        <input type="text" class="form-control" id="postalCode">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>نوع المورد</label>
                        <select class="form-control" id="supplierType">
                            <option value="manufacturer">مصنع</option>
                            <option value="distributor">موزع</option>
                            <option value="wholesaler">تاجر جملة</option>
                            <option value="retailer">تاجر تجزئة</option>
                            <option value="service">خدمات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الحالة</label>
                        <select class="form-control" id="status">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>الرقم الضريبي</label>
                        <input type="text" class="form-control" id="taxNumber">
                    </div>
                    <div class="form-group">
                        <label>رقم السجل التجاري</label>
                        <input type="text" class="form-control" id="commercialRegister">
                    </div>
                </div>
                
                <div class="form-row full">
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                    </div>
                </div>
                
                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let suppliers = [];
        let currentPage = 1;
        let totalPages = 1;
        const itemsPerPage = 10;

        // تحميل الموردين
        async function loadSuppliers() {
            try {
                document.getElementById('loadingIndicator').style.display = 'block';
                document.getElementById('suppliersContent').style.display = 'none';
                document.getElementById('emptyState').style.display = 'none';

                const response = await fetch('/api/suppliers');
                const data = await response.json();

                if (data.success) {
                    suppliers = data.data || [];
                    displaySuppliers();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الموردين:', error);
                // عرض بيانات تجريبية
                suppliers = [
                    {
                        id: 1,
                        supplierName: 'شركة التقنية المتقدمة',
                        contactName: 'محمد أحمد',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        address: 'الرياض، حي الملك فهد',
                        city: 'الرياض',
                        supplierType: 'manufacturer',
                        status: 'active',
                        taxNumber: '123456789012345',
                        createdAt: '2024-01-15'
                    },
                    {
                        id: 2,
                        supplierName: 'مؤسسة الإلكترونيات الحديثة',
                        contactName: 'سارة علي',
                        phone: '0507654321',
                        email: '<EMAIL>',
                        address: 'جدة، حي الحمراء',
                        city: 'جدة',
                        supplierType: 'distributor',
                        status: 'active',
                        taxNumber: '987654321098765',
                        createdAt: '2024-01-20'
                    }
                ];
                displaySuppliers();
            }
        }

        // عرض الموردين
        function displaySuppliers() {
            const tbody = document.getElementById('suppliersTableBody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageSuppliers = suppliers.slice(startIndex, endIndex);

            if (suppliers.length === 0) {
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('emptyState').style.display = 'block';
                return;
            }

            tbody.innerHTML = '';
            pageSuppliers.forEach(supplier => {
                const supplierTypeText = {
                    'manufacturer': 'مصنع',
                    'distributor': 'موزع',
                    'wholesaler': 'تاجر جملة',
                    'retailer': 'تاجر تجزئة',
                    'service': 'خدمات'
                };

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${supplier.id}</td>
                    <td>${supplier.supplierName}</td>
                    <td>${supplier.phone}</td>
                    <td>${supplier.email || '-'}</td>
                    <td>${supplier.address || '-'}</td>
                    <td>${supplierTypeText[supplier.supplierType] || supplier.supplierType}</td>
                    <td>
                        <span class="status-badge ${supplier.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${supplier.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>${new Date(supplier.createdAt).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-info btn-sm" onclick="viewSupplier(${supplier.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="editSupplier(${supplier.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteSupplier(${supplier.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('suppliersContent').style.display = 'block';
            
            updatePagination();
        }

        // تحديث التصفح
        function updatePagination() {
            totalPages = Math.ceil(suppliers.length / itemsPerPage);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                const button = document.createElement('button');
                button.textContent = i;
                button.className = i === currentPage ? 'active' : '';
                button.onclick = () => {
                    currentPage = i;
                    displaySuppliers();
                };
                pagination.appendChild(button);
            }
        }

        // فتح نافذة الإضافة
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة مورد جديد';
            document.getElementById('supplierForm').reset();
            document.getElementById('supplierId').value = '';
            document.getElementById('supplierModal').style.display = 'block';
        }

        // إغلاق النافذة
        function closeModal() {
            document.getElementById('supplierModal').style.display = 'none';
        }

        // حفظ المورد
        document.getElementById('supplierForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                supplierName: document.getElementById('supplierName').value,
                contactName: document.getElementById('contactName').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                address: document.getElementById('address').value,
                city: document.getElementById('city').value,
                postalCode: document.getElementById('postalCode').value,
                supplierType: document.getElementById('supplierType').value,
                status: document.getElementById('status').value,
                taxNumber: document.getElementById('taxNumber').value,
                commercialRegister: document.getElementById('commercialRegister').value,
                notes: document.getElementById('notes').value
            };

            try {
                const supplierId = document.getElementById('supplierId').value;
                const url = supplierId ? `/api/suppliers/${supplierId}` : '/api/suppliers';
                const method = supplierId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (data.success) {
                    alert(supplierId ? 'تم تحديث المورد بنجاح' : 'تم إضافة المورد بنجاح');
                    closeModal();
                    loadSuppliers();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            } catch (error) {
                console.error('خطأ في حفظ المورد:', error);
                alert('حدث خطأ في حفظ البيانات');
            }
        });

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadSuppliers();
        });

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('supplierModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
