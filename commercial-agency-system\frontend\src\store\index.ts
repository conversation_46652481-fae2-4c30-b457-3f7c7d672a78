import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { 
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';

// استيراد المقللات (Reducers)
import authReducer from './slices/authSlice';
import uiReducer from './slices/uiSlice';
import settingsReducer from './slices/settingsSlice';
// import customerReducer from './slices/customerSlice';
// import supplierReducer from './slices/supplierSlice';
// import productReducer from './slices/productSlice';
// import inventoryReducer from './slices/inventorySlice';
// import salesReducer from './slices/salesSlice';
// import purchaseReducer from './slices/purchaseSlice';
// import paymentReducer from './slices/paymentSlice';

// إعدادات Redux Persist
const persistConfig = {
  key: 'commercial-agency-root',
  version: 1,
  storage,
  whitelist: ['auth', 'settings'], // المقللات التي سيتم حفظها
  blacklist: ['ui'], // المقللات التي لن يتم حفظها
};

// إعدادات خاصة للمصادقة
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['user', 'token', 'refreshToken', 'isAuthenticated'],
};

// إعدادات خاصة للإعدادات
const settingsPersistConfig = {
  key: 'settings',
  storage,
  whitelist: ['theme', 'language', 'notifications', 'layout'],
};

// دمج المقللات
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authReducer),
  ui: uiReducer,
  settings: persistReducer(settingsPersistConfig, settingsReducer),
  // customers: customerReducer,
  // suppliers: supplierReducer,
  // products: productReducer,
  // inventory: inventoryReducer,
  // sales: salesReducer,
  // purchases: purchaseReducer,
  // payments: paymentReducer,
});

// تطبيق Redux Persist على المقلل الجذر
const persistedReducer = persistReducer(persistConfig, rootReducer);

// إنشاء المتجر
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        ignoredPaths: ['items.dates'],
      },
      immutableCheck: {
        ignoredPaths: ['items.dates'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
  preloadedState: undefined,
});

// إنشاء persistor
export const persistor = persistStore(store);

// تصدير الأنواع
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Hooks مخصصة للـ TypeScript
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// دوال مساعدة للمتجر
export const getState = () => store.getState();
export const dispatch = store.dispatch;

// دالة لإعادة تعيين المتجر
export const resetStore = () => {
  persistor.purge();
  window.location.reload();
};

// دالة للحصول على حالة المصادقة
export const getAuthState = () => getState().auth;

// دالة للحصول على حالة الإعدادات
export const getSettingsState = () => getState().settings;

// دالة للحصول على حالة واجهة المستخدم
export const getUIState = () => getState().ui;

// Middleware مخصص للتسجيل في بيئة التطوير
const loggerMiddleware = (store: any) => (next: any) => (action: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`Action: ${action.type}`);
    console.log('Previous State:', store.getState());
    console.log('Action:', action);
    const result = next(action);
    console.log('Next State:', store.getState());
    console.groupEnd();
    return result;
  }
  return next(action);
};

// إضافة middleware مخصص في بيئة التطوير
if (process.env.NODE_ENV === 'development') {
  // يمكن إضافة middleware إضافي هنا
}

// تصدير افتراضي
export default store;
