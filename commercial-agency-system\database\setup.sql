-- إعد<PERSON> قاعدة البيانات للوكالة التجارية
-- Database Setup for Commercial Agency System

-- إنشاء المستخدم الخاص بقاعدة البيانات
CREATE USER IF NOT EXISTS 'commercial_user'@'localhost' IDENTIFIED BY 'Commercial@2025!';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON commercial_agency_db.* TO 'commercial_user'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON commercial_agency_db.* TO 'commercial_user'@'localhost';

-- تحديث الصلاحيات
FLUSH PRIVILEGES;

-- استخدام قاعدة البيانات
USE commercial_agency_db;

-- إعداد المتغيرات
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+03:00";

-- تفعيل الفهرسة النصية
SET GLOBAL innodb_ft_min_token_size = 2;
SET GLOBAL innodb_ft_enable_stopword = 0;

-- إعدادات الأداء
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;

-- إعادة تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;

COMMIT;

-- إنشاء إجراءات مخزنة للعمليات الشائعة

DELIMITER //

-- إجراء لحساب رصيد العميل
CREATE PROCEDURE CalculateCustomerBalance(IN customer_id INT)
BEGIN
    DECLARE total_invoices DECIMAL(15,2) DEFAULT 0;
    DECLARE total_payments DECIMAL(15,2) DEFAULT 0;
    DECLARE current_balance DECIMAL(15,2) DEFAULT 0;
    
    -- حساب إجمالي الفواتير
    SELECT COALESCE(SUM(total_amount), 0) INTO total_invoices
    FROM sales_invoices 
    WHERE customer_id = customer_id AND status != 'cancelled';
    
    -- حساب إجمالي المدفوعات
    SELECT COALESCE(SUM(amount), 0) INTO total_payments
    FROM payments 
    WHERE customer_id = customer_id AND payment_type = 'received' AND status = 'completed';
    
    -- حساب الرصيد الحالي
    SET current_balance = total_invoices - total_payments;
    
    -- تحديث جدول أرصدة العملاء
    INSERT INTO customer_balances (customer_id, balance, last_transaction_date)
    VALUES (customer_id, current_balance, CURDATE())
    ON DUPLICATE KEY UPDATE 
        balance = current_balance,
        last_transaction_date = CURDATE();
END //

-- إجراء لحساب رصيد المورد
CREATE PROCEDURE CalculateSupplierBalance(IN supplier_id INT)
BEGIN
    DECLARE total_invoices DECIMAL(15,2) DEFAULT 0;
    DECLARE total_payments DECIMAL(15,2) DEFAULT 0;
    DECLARE current_balance DECIMAL(15,2) DEFAULT 0;
    
    -- حساب إجمالي الفواتير
    SELECT COALESCE(SUM(total_amount), 0) INTO total_invoices
    FROM purchase_invoices 
    WHERE supplier_id = supplier_id AND status != 'cancelled';
    
    -- حساب إجمالي المدفوعات
    SELECT COALESCE(SUM(amount), 0) INTO total_payments
    FROM payments 
    WHERE supplier_id = supplier_id AND payment_type = 'paid' AND status = 'completed';
    
    -- حساب الرصيد الحالي
    SET current_balance = total_invoices - total_payments;
    
    -- تحديث جدول أرصدة الموردين
    INSERT INTO supplier_balances (supplier_id, balance, last_transaction_date)
    VALUES (supplier_id, current_balance, CURDATE())
    ON DUPLICATE KEY UPDATE 
        balance = current_balance,
        last_transaction_date = CURDATE();
END //

-- إجراء لتحديث المخزون
CREATE PROCEDURE UpdateInventory(
    IN p_product_id INT,
    IN p_warehouse_id INT,
    IN p_quantity DECIMAL(15,3),
    IN p_movement_type ENUM('in', 'out'),
    IN p_reference_type VARCHAR(50),
    IN p_reference_id INT,
    IN p_unit_cost DECIMAL(15,2),
    IN p_user_id INT
)
BEGIN
    DECLARE current_quantity DECIMAL(15,3) DEFAULT 0;
    DECLARE new_quantity DECIMAL(15,3) DEFAULT 0;
    
    -- الحصول على الكمية الحالية
    SELECT COALESCE(quantity_on_hand, 0) INTO current_quantity
    FROM inventory 
    WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id;
    
    -- حساب الكمية الجديدة
    IF p_movement_type = 'in' THEN
        SET new_quantity = current_quantity + p_quantity;
    ELSE
        SET new_quantity = current_quantity - p_quantity;
    END IF;
    
    -- تحديث المخزون
    INSERT INTO inventory (product_id, warehouse_id, quantity_on_hand, last_purchase_price)
    VALUES (p_product_id, p_warehouse_id, new_quantity, p_unit_cost)
    ON DUPLICATE KEY UPDATE 
        quantity_on_hand = new_quantity,
        last_purchase_price = COALESCE(p_unit_cost, last_purchase_price),
        last_updated = CURRENT_TIMESTAMP;
    
    -- إضافة حركة المخزون
    INSERT INTO inventory_movements (
        product_id, warehouse_id, movement_type, reference_type, reference_id,
        quantity, unit_cost, total_cost, created_by
    ) VALUES (
        p_product_id, p_warehouse_id, p_movement_type, p_reference_type, p_reference_id,
        p_quantity, p_unit_cost, (p_quantity * COALESCE(p_unit_cost, 0)), p_user_id
    );
END //

-- إجراء لتوليد رقم تلقائي
CREATE FUNCTION GenerateNumber(prefix VARCHAR(10), table_name VARCHAR(50), column_name VARCHAR(50)) 
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE next_number INT DEFAULT 1;
    DECLARE formatted_number VARCHAR(50);
    
    -- الحصول على آخر رقم
    SET @sql = CONCAT('SELECT COALESCE(MAX(CAST(SUBSTRING(', column_name, ', LENGTH("', prefix, '") + 1) AS UNSIGNED)), 0) + 1 INTO @next_number FROM ', table_name, ' WHERE ', column_name, ' LIKE "', prefix, '%"');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET next_number = @next_number;
    SET formatted_number = CONCAT(prefix, LPAD(next_number, 6, '0'));
    
    RETURN formatted_number;
END //

-- إجراء للنسخ الاحتياطي التلقائي
CREATE PROCEDURE CreateBackup()
BEGIN
    DECLARE backup_name VARCHAR(100);
    SET backup_name = CONCAT('backup_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));
    
    -- هنا يمكن إضافة كود النسخ الاحتياطي
    INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, created_at)
    VALUES (1, 'BACKUP', 'system', 0, JSON_OBJECT('backup_name', backup_name), NOW());
END //

DELIMITER ;

-- إنشاء المشغلات (Triggers) للتدقيق التلقائي

-- مشغل لتسجيل تعديل المستخدمين
DELIMITER //
CREATE TRIGGER users_audit_update
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values)
    VALUES (
        NEW.id,
        'UPDATE',
        'users',
        NEW.id,
        JSON_OBJECT('username', OLD.username, 'email', OLD.email, 'role_id', OLD.role_id),
        JSON_OBJECT('username', NEW.username, 'email', NEW.email, 'role_id', NEW.role_id)
    );
END //

-- مشغل لتسجيل حذف المستخدمين
CREATE TRIGGER users_audit_delete
BEFORE DELETE ON users
FOR EACH ROW
BEGIN
    INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values)
    VALUES (
        OLD.id,
        'DELETE',
        'users',
        OLD.id,
        JSON_OBJECT('username', OLD.username, 'email', OLD.email, 'role_id', OLD.role_id)
    );
END //

-- مشغل لتحديث رصيد العميل عند إنشاء فاتورة
CREATE TRIGGER sales_invoice_balance_update
AFTER INSERT ON sales_invoices
FOR EACH ROW
BEGIN
    CALL CalculateCustomerBalance(NEW.customer_id);
END //

-- مشغل لتحديث رصيد المورد عند إنشاء فاتورة شراء
CREATE TRIGGER purchase_invoice_balance_update
AFTER INSERT ON purchase_invoices
FOR EACH ROW
BEGIN
    CALL CalculateSupplierBalance(NEW.supplier_id);
END //

-- مشغل لتحديث المخزون عند إنشاء فاتورة بيع
CREATE TRIGGER sales_invoice_inventory_update
AFTER INSERT ON sales_invoice_items
FOR EACH ROW
BEGIN
    CALL UpdateInventory(
        NEW.product_id,
        (SELECT warehouse_id FROM sales_invoices WHERE id = NEW.sales_invoice_id),
        NEW.quantity,
        'out',
        'sale',
        NEW.sales_invoice_id,
        NEW.cost_price,
        (SELECT created_by FROM sales_invoices WHERE id = NEW.sales_invoice_id)
    );
END //

-- مشغل لتحديث المخزون عند إنشاء فاتورة شراء
CREATE TRIGGER purchase_invoice_inventory_update
AFTER INSERT ON purchase_invoice_items
FOR EACH ROW
BEGIN
    CALL UpdateInventory(
        NEW.product_id,
        (SELECT warehouse_id FROM purchase_invoices WHERE id = NEW.purchase_invoice_id),
        NEW.quantity,
        'in',
        'purchase',
        NEW.purchase_invoice_id,
        NEW.unit_price,
        (SELECT created_by FROM purchase_invoices WHERE id = NEW.purchase_invoice_id)
    );
END //

DELIMITER ;

-- إنشاء أحداث مجدولة للصيانة التلقائية
SET GLOBAL event_scheduler = ON;

-- حدث لتنظيف الجلسات المنتهية الصلاحية
CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
ON SCHEDULE EVERY 1 HOUR
DO
  DELETE FROM user_sessions WHERE expires_at < NOW();

-- حدث للنسخ الاحتياطي اليومي
CREATE EVENT IF NOT EXISTS daily_backup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CreateBackup();

-- حدث لتحديث إحصائيات الجداول
CREATE EVENT IF NOT EXISTS update_table_stats
ON SCHEDULE EVERY 1 WEEK
DO
  ANALYZE TABLE users, customers, suppliers, products, inventory, sales_invoices, purchase_invoices;

COMMIT;
