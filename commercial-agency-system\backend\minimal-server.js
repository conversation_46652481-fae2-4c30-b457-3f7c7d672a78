const http = require('http');

const server = http.createServer((req, res) => {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  res.setHeader('Content-Type', 'application/json');

  if (req.url === '/health') {
    res.writeHead(200);
    res.end(JSON.stringify({ status: 'OK', message: 'Server is running' }));
    return;
  }

  if (req.url === '/api/test') {
    res.writeHead(200);
    res.end(JSON.stringify({ message: 'API is working!' }));
    return;
  }

  if (req.url === '/api/auth/login' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        if (data.username === 'admin' && data.password === 'Admin@123456') {
          res.writeHead(200);
          res.end(JSON.stringify({
            success: true,
            data: {
              user: {
                id: 1,
                username: 'admin',
                firstName: 'مدير',
                lastName: 'النظام',
                fullName: 'مدير النظام',
                role: { name: 'admin', nameAr: 'مدير النظام', permissions: ['admin.all'] }
              },
              accessToken: 'mock-token-' + Date.now()
            }
          }));
        } else {
          res.writeHead(401);
          res.end(JSON.stringify({ success: false, message: 'خطأ في البيانات' }));
        }
      } catch (e) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'خطأ في البيانات' }));
      }
    });
    return;
  }

  res.writeHead(404);
  res.end(JSON.stringify({ message: 'Not found' }));
});

const PORT = 3001;
server.listen(PORT, 'localhost', () => {
  console.log(`Server running on http://localhost:${PORT}`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});
