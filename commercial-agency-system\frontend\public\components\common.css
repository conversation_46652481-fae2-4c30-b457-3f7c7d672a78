/* Common Styles for Commercial Agency System */

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 5px;
    cursor: pointer;
    margin: 0 2px;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Status Badges */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.status-active, .status-paid {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.status-inactive, .status-unpaid {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.status-pending, .status-partial {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.status-completed {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.status-cancelled {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-sm {
    padding: 8px 12px;
    font-size: 0.85rem;
    border-radius: 6px;
}

.btn-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    border: none;
}

/* Loading States */
.loading {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
    color: #667eea;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.empty-state p {
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Form Validation */
.form-control.error {
    border-color: #e74c3c;
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
}

.form-control.success {
    border-color: #27ae60;
    box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
}

.error-message {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 5px;
    display: block;
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #2c3e50;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Search Highlight */
.search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* Responsive Tables */
@media (max-width: 768px) {
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .table {
        min-width: 600px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .btn-sm {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .page-header,
    .filters-section,
    .action-buttons {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
        padding-top: 0 !important;
    }
    
    .container {
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .status-badge {
        filter: brightness(1.2);
    }
    
    .pagination-btn {
        background: #2c3e50;
        color: white;
        border-color: #34495e;
    }
    
    .pagination-btn:hover {
        background: #34495e;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
.btn:focus,
.form-control:focus,
.pagination-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
