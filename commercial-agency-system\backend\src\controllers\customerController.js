const { Customer } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// الحصول على جميع العملاء
const getCustomers = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      type = 'all',
      category = 'all',
      status = 'active',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // بناء شروط البحث
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { customer_code: { [Op.like]: `%${search}%` } },
        { first_name: { [Op.like]: `%${search}%` } },
        { last_name: { [Op.like]: `%${search}%` } },
        { company_name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { phone: { [Op.like]: `%${search}%` } },
        { mobile: { [Op.like]: `%${search}%` } }
      ];
    }
    
    if (type !== 'all') {
      whereClause.customer_type = type;
    }
    
    if (category !== 'all') {
      whereClause.customer_category = category;
    }
    
    if (status === 'active') {
      whereClause.is_active = true;
    } else if (status === 'inactive') {
      whereClause.is_active = false;
    }

    const { count, rows: customers } = await Customer.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: offset,
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    const customersData = customers.map(customer => ({
      id: customer.id,
      customerCode: customer.customer_code,
      customerType: customer.customer_type,
      customerTypeName: customer.getTypeName(),
      displayName: customer.getDisplayName(),
      firstName: customer.first_name,
      lastName: customer.last_name,
      companyName: customer.company_name,
      email: customer.email,
      phone: customer.phone,
      mobile: customer.mobile,
      address: customer.address,
      city: customer.city,
      country: customer.country,
      customerCategory: customer.customer_category,
      customerCategoryName: customer.getCategoryName(),
      creditLimit: customer.credit_limit,
      paymentTerms: customer.payment_terms,
      discountPercentage: customer.discount_percentage,
      currency: customer.currency,
      isActive: customer.is_active,
      createdAt: customer.created_at,
      updatedAt: customer.updated_at
    }));

    res.paginated(customersData, {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count
    }, 'تم جلب العملاء بنجاح');

  } catch (error) {
    console.error('خطأ في جلب العملاء:', error);
    res.error('حدث خطأ في جلب العملاء', 500);
  }
};

// الحصول على عميل واحد
const getCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id);

    if (!customer) {
      return res.error('العميل غير موجود', 404);
    }

    const customerData = {
      id: customer.id,
      customerCode: customer.customer_code,
      customerType: customer.customer_type,
      customerTypeName: customer.getTypeName(),
      displayName: customer.getDisplayName(),
      firstName: customer.first_name,
      lastName: customer.last_name,
      companyName: customer.company_name,
      email: customer.email,
      phone: customer.phone,
      mobile: customer.mobile,
      taxNumber: customer.tax_number,
      commercialRegister: customer.commercial_register,
      nationalId: customer.national_id,
      address: customer.address,
      fullAddress: customer.getFullAddress(),
      city: customer.city,
      country: customer.country,
      postalCode: customer.postal_code,
      customerCategory: customer.customer_category,
      customerCategoryName: customer.getCategoryName(),
      creditLimit: customer.credit_limit,
      paymentTerms: customer.payment_terms,
      discountPercentage: customer.discount_percentage,
      currency: customer.currency,
      notes: customer.notes,
      isActive: customer.is_active,
      createdAt: customer.created_at,
      updatedAt: customer.updated_at
    };

    res.success(customerData, 'تم جلب العميل بنجاح');

  } catch (error) {
    console.error('خطأ في جلب العميل:', error);
    res.error('حدث خطأ في جلب العميل', 500);
  }
};

// إنشاء عميل جديد
const createCustomer = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const customerData = {
      ...req.body,
      created_by: req.user.id
    };

    // تحويل أسماء الحقول
    if (customerData.customerType) {
      customerData.customer_type = customerData.customerType;
      delete customerData.customerType;
    }
    if (customerData.firstName) {
      customerData.first_name = customerData.firstName;
      delete customerData.firstName;
    }
    if (customerData.lastName) {
      customerData.last_name = customerData.lastName;
      delete customerData.lastName;
    }
    if (customerData.companyName) {
      customerData.company_name = customerData.companyName;
      delete customerData.companyName;
    }
    if (customerData.taxNumber) {
      customerData.tax_number = customerData.taxNumber;
      delete customerData.taxNumber;
    }
    if (customerData.commercialRegister) {
      customerData.commercial_register = customerData.commercialRegister;
      delete customerData.commercialRegister;
    }
    if (customerData.nationalId) {
      customerData.national_id = customerData.nationalId;
      delete customerData.nationalId;
    }
    if (customerData.postalCode) {
      customerData.postal_code = customerData.postalCode;
      delete customerData.postalCode;
    }
    if (customerData.customerCategory) {
      customerData.customer_category = customerData.customerCategory;
      delete customerData.customerCategory;
    }
    if (customerData.creditLimit) {
      customerData.credit_limit = customerData.creditLimit;
      delete customerData.creditLimit;
    }
    if (customerData.paymentTerms) {
      customerData.payment_terms = customerData.paymentTerms;
      delete customerData.paymentTerms;
    }
    if (customerData.discountPercentage) {
      customerData.discount_percentage = customerData.discountPercentage;
      delete customerData.discountPercentage;
    }
    if (customerData.isActive !== undefined) {
      customerData.is_active = customerData.isActive;
      delete customerData.isActive;
    }

    const customer = await Customer.createCustomer(customerData);

    const responseData = {
      id: customer.id,
      customerCode: customer.customer_code,
      customerType: customer.customer_type,
      displayName: customer.getDisplayName(),
      firstName: customer.first_name,
      lastName: customer.last_name,
      companyName: customer.company_name,
      email: customer.email,
      phone: customer.phone,
      mobile: customer.mobile,
      customerCategory: customer.customer_category,
      creditLimit: customer.credit_limit,
      isActive: customer.is_active,
      createdAt: customer.created_at
    };

    res.success(responseData, 'تم إنشاء العميل بنجاح', 201);

  } catch (error) {
    console.error('خطأ في إنشاء العميل:', error);
    if (error.message.includes('مستخدم بالفعل')) {
      res.error(error.message, 400);
    } else {
      res.error('حدث خطأ في إنشاء العميل', 500);
    }
  }
};

// تحديث عميل
const updateCustomer = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const { id } = req.params;
    const updateData = { ...req.body };

    // تحويل أسماء الحقول
    if (updateData.customerType) {
      updateData.customer_type = updateData.customerType;
      delete updateData.customerType;
    }
    if (updateData.firstName) {
      updateData.first_name = updateData.firstName;
      delete updateData.firstName;
    }
    if (updateData.lastName) {
      updateData.last_name = updateData.lastName;
      delete updateData.lastName;
    }
    if (updateData.companyName) {
      updateData.company_name = updateData.companyName;
      delete updateData.companyName;
    }
    if (updateData.taxNumber) {
      updateData.tax_number = updateData.taxNumber;
      delete updateData.taxNumber;
    }
    if (updateData.commercialRegister) {
      updateData.commercial_register = updateData.commercialRegister;
      delete updateData.commercialRegister;
    }
    if (updateData.nationalId) {
      updateData.national_id = updateData.nationalId;
      delete updateData.nationalId;
    }
    if (updateData.postalCode) {
      updateData.postal_code = updateData.postalCode;
      delete updateData.postalCode;
    }
    if (updateData.customerCategory) {
      updateData.customer_category = updateData.customerCategory;
      delete updateData.customerCategory;
    }
    if (updateData.creditLimit) {
      updateData.credit_limit = updateData.creditLimit;
      delete updateData.creditLimit;
    }
    if (updateData.paymentTerms) {
      updateData.payment_terms = updateData.paymentTerms;
      delete updateData.paymentTerms;
    }
    if (updateData.discountPercentage) {
      updateData.discount_percentage = updateData.discountPercentage;
      delete updateData.discountPercentage;
    }
    if (updateData.isActive !== undefined) {
      updateData.is_active = updateData.isActive;
      delete updateData.isActive;
    }

    const updatedCustomer = await Customer.updateCustomer(id, updateData);

    const responseData = {
      id: updatedCustomer.id,
      customerCode: updatedCustomer.customer_code,
      customerType: updatedCustomer.customer_type,
      displayName: updatedCustomer.getDisplayName(),
      firstName: updatedCustomer.first_name,
      lastName: updatedCustomer.last_name,
      companyName: updatedCustomer.company_name,
      email: updatedCustomer.email,
      phone: updatedCustomer.phone,
      mobile: updatedCustomer.mobile,
      customerCategory: updatedCustomer.customer_category,
      creditLimit: updatedCustomer.credit_limit,
      isActive: updatedCustomer.is_active,
      updatedAt: updatedCustomer.updated_at
    };

    res.success(responseData, 'تم تحديث العميل بنجاح');

  } catch (error) {
    console.error('خطأ في تحديث العميل:', error);
    if (error.message === 'العميل غير موجود') {
      res.error(error.message, 404);
    } else if (error.message.includes('مستخدم بالفعل')) {
      res.error(error.message, 400);
    } else {
      res.error('حدث خطأ في تحديث العميل', 500);
    }
  }
};

// حذف عميل
const deleteCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedCustomer = await Customer.deleteCustomer(id);

    res.success({
      id: deletedCustomer.id,
      customerCode: deletedCustomer.customer_code,
      displayName: deletedCustomer.getDisplayName(),
      isActive: deletedCustomer.is_active
    }, 'تم حذف العميل بنجاح');

  } catch (error) {
    console.error('خطأ في حذف العميل:', error);
    if (error.message === 'العميل غير موجود') {
      res.error(error.message, 404);
    } else if (error.message.includes('فواتير مرتبطة')) {
      res.error(error.message, 400);
    } else {
      res.error('حدث خطأ في حذف العميل', 500);
    }
  }
};

// البحث في العملاء
const searchCustomers = async (req, res) => {
  try {
    const { q: searchTerm, limit = 10 } = req.query;

    if (!searchTerm || searchTerm.length < 2) {
      return res.error('يجب أن يكون مصطلح البحث أكثر من حرفين', 400);
    }

    const customers = await Customer.searchCustomers(searchTerm, {
      limit: parseInt(limit)
    });

    const customersData = customers.map(customer => ({
      id: customer.id,
      customerCode: customer.customer_code,
      displayName: customer.getDisplayName(),
      customerType: customer.customer_type,
      customerCategory: customer.customer_category,
      phone: customer.phone,
      email: customer.email,
      isActive: customer.is_active
    }));

    res.success(customersData, 'تم البحث بنجاح');

  } catch (error) {
    console.error('خطأ في البحث:', error);
    res.error('حدث خطأ في البحث', 500);
  }
};

// الحصول على إحصائيات العملاء
const getCustomerStats = async (req, res) => {
  try {
    const stats = await Customer.getCustomerStats();
    const categoryStats = await Customer.getCustomersByCategory();

    res.success({
      ...stats,
      categoryDistribution: categoryStats
    }, 'تم جلب الإحصائيات بنجاح');

  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    res.error('حدث خطأ في جلب الإحصائيات', 500);
  }
};

// الحصول على العميل بالرمز
const getCustomerByCode = async (req, res) => {
  try {
    const { code } = req.params;

    const customer = await Customer.findByCode(code);

    if (!customer) {
      return res.error('العميل غير موجود', 404);
    }

    const customerData = {
      id: customer.id,
      customerCode: customer.customer_code,
      displayName: customer.getDisplayName(),
      customerType: customer.customer_type,
      customerCategory: customer.customer_category,
      creditLimit: customer.credit_limit,
      discountPercentage: customer.discount_percentage,
      paymentTerms: customer.payment_terms
    };

    res.success(customerData, 'تم جلب العميل بنجاح');

  } catch (error) {
    console.error('خطأ في جلب العميل:', error);
    res.error('حدث خطأ في جلب العميل', 500);
  }
};

module.exports = {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  searchCustomers,
  getCustomerStats,
  getCustomerByCode
};
