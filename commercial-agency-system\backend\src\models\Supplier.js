const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// نموذج الموردين
const Supplier = sequelize.define('Supplier', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  supplier_code: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    field: 'supplier_code'
  },
  company_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'company_name',
    validate: {
      len: [2, 100],
      notEmpty: true
    }
  },
  contact_person: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'contact_person'
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^[\+]?[0-9\-\(\)\s]+$/
    }
  },
  mobile: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^[\+]?[0-9\-\(\)\s]+$/
    }
  },
  fax: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  website: {
    type: DataTypes.STRING(100),
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  tax_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'tax_number'
  },
  commercial_register: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'commercial_register'
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  country: {
    type: DataTypes.STRING(50),
    defaultValue: 'Saudi Arabia'
  },
  postal_code: {
    type: DataTypes.STRING(10),
    allowNull: true,
    field: 'postal_code'
  },
  credit_limit: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'credit_limit'
  },
  payment_terms: {
    type: DataTypes.INTEGER,
    defaultValue: 30,
    field: 'payment_terms'
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'SAR'
  },
  supplier_type: {
    type: DataTypes.ENUM('local', 'international'),
    defaultValue: 'local',
    field: 'supplier_type'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0,
    validate: {
      min: 0,
      max: 5
    }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'updated_at'
  }
}, {
  tableName: 'suppliers',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['supplier_code']
    },
    {
      fields: ['supplier_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['company_name']
    }
  ]
});

// الدوال الإضافية للنموذج
Supplier.prototype.getFullAddress = function() {
  const addressParts = [this.address, this.city, this.country].filter(Boolean);
  return addressParts.join(', ');
};

Supplier.prototype.getTypeName = function() {
  const types = {
    local: 'محلي',
    international: 'دولي'
  };
  return types[this.supplier_type] || this.supplier_type;
};

Supplier.prototype.getRatingStars = function() {
  const rating = parseFloat(this.rating) || 0;
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  return {
    full: fullStars,
    half: hasHalfStar ? 1 : 0,
    empty: emptyStars,
    value: rating
  };
};

Supplier.prototype.updateRating = async function(newRating) {
  if (newRating < 0 || newRating > 5) {
    throw new Error('التقييم يجب أن يكون بين 0 و 5');
  }
  
  this.rating = newRating;
  await this.save();
};

// الدوال الثابتة
Supplier.generateSupplierCode = async function() {
  const lastSupplier = await this.findOne({
    order: [['id', 'DESC']],
    attributes: ['supplier_code']
  });
  
  let nextNumber = 1;
  if (lastSupplier && lastSupplier.supplier_code) {
    const lastNumber = parseInt(lastSupplier.supplier_code.replace('SUP', ''));
    nextNumber = lastNumber + 1;
  }
  
  return `SUP${nextNumber.toString().padStart(6, '0')}`;
};

Supplier.findByCode = async function(supplierCode) {
  return await this.findOne({
    where: { supplier_code: supplierCode, is_active: true }
  });
};

Supplier.findActiveSuppliers = async function(options = {}) {
  return await this.findAll({
    where: { is_active: true },
    ...options
  });
};

Supplier.createSupplier = async function(supplierData) {
  try {
    if (!supplierData.supplier_code) {
      supplierData.supplier_code = await this.generateSupplierCode();
    }
    
    const supplier = await this.create(supplierData);
    return supplier;
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      throw new Error('رمز المورد مستخدم بالفعل');
    }
    throw error;
  }
};

Supplier.updateSupplier = async function(id, supplierData) {
  const supplier = await this.findByPk(id);
  if (!supplier) {
    throw new Error('المورد غير موجود');
  }
  
  await supplier.update(supplierData);
  return supplier;
};

Supplier.deleteSupplier = async function(id) {
  const supplier = await this.findByPk(id);
  if (!supplier) {
    throw new Error('المورد غير موجود');
  }
  
  // التحقق من عدم وجود فواتير مرتبطة
  const PurchaseInvoice = require('./PurchaseInvoice');
  const invoicesCount = await PurchaseInvoice.count({ 
    where: { supplier_id: id, status: { [sequelize.Op.ne]: 'cancelled' } }
  });
  
  if (invoicesCount > 0) {
    throw new Error('لا يمكن حذف المورد لوجود فواتير مرتبطة به');
  }
  
  // حذف منطقي
  await supplier.update({ is_active: false });
  return supplier;
};

Supplier.searchSuppliers = async function(searchTerm, options = {}) {
  const { Op } = require('sequelize');
  
  return await this.findAll({
    where: {
      is_active: true,
      [Op.or]: [
        { supplier_code: { [Op.like]: `%${searchTerm}%` } },
        { company_name: { [Op.like]: `%${searchTerm}%` } },
        { contact_person: { [Op.like]: `%${searchTerm}%` } },
        { email: { [Op.like]: `%${searchTerm}%` } },
        { phone: { [Op.like]: `%${searchTerm}%` } },
        { mobile: { [Op.like]: `%${searchTerm}%` } }
      ]
    },
    ...options
  });
};

Supplier.getSupplierStats = async function() {
  const { Op } = require('sequelize');
  
  const totalSuppliers = await this.count({ where: { is_active: true } });
  const localSuppliers = await this.count({ 
    where: { supplier_type: 'local', is_active: true } 
  });
  const internationalSuppliers = await this.count({ 
    where: { supplier_type: 'international', is_active: true } 
  });
  const highRatedSuppliers = await this.count({ 
    where: { rating: { [Op.gte]: 4 }, is_active: true } 
  });
  
  return {
    total: totalSuppliers,
    local: localSuppliers,
    international: internationalSuppliers,
    highRated: highRatedSuppliers
  };
};

Supplier.getSuppliersByType = async function() {
  const results = await this.findAll({
    attributes: [
      'supplier_type',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    where: { is_active: true },
    group: ['supplier_type']
  });
  
  return results.map(result => ({
    type: result.supplier_type,
    count: parseInt(result.dataValues.count)
  }));
};

Supplier.getTopSuppliers = async function(limit = 10) {
  // سيتم تنفيذ هذا عند إضافة نموذج فواتير الشراء
  return [];
};

Supplier.getSuppliersByRating = async function() {
  const results = await this.findAll({
    attributes: [
      [sequelize.fn('FLOOR', sequelize.col('rating')), 'rating_floor'],
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    where: { is_active: true },
    group: [sequelize.fn('FLOOR', sequelize.col('rating'))],
    order: [[sequelize.fn('FLOOR', sequelize.col('rating')), 'DESC']]
  });
  
  return results.map(result => ({
    rating: parseInt(result.dataValues.rating_floor),
    count: parseInt(result.dataValues.count)
  }));
};

// تصدير النموذج
module.exports = Supplier;
