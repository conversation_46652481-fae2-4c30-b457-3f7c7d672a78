const { User, Role } = require('../models');
const { validationResult } = require('express-validator');
const { validatePasswordStrength } = require('../config/auth');
const { Op } = require('sequelize');

// الحصول على جميع المستخدمين
const getUsers = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      role = '', 
      status = 'all',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // بناء شروط البحث
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { username: { [Op.like]: `%${search}%` } },
        { first_name: { [Op.like]: `%${search}%` } },
        { last_name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }
    
    if (status !== 'all') {
      whereClause.is_active = status === 'active';
    }

    // شروط الدور
    const includeClause = [{
      model: Role,
      as: 'role',
      attributes: ['id', 'name', 'name_ar', 'permissions']
    }];

    if (role) {
      includeClause[0].where = { name: role };
    }

    const { count, rows: users } = await User.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    const usersData = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      fullName: user.getFullName(),
      phone: user.phone,
      avatar: user.avatar,
      isActive: user.is_active,
      emailVerified: user.email_verified,
      lastLogin: user.last_login,
      loginAttempts: user.login_attempts,
      isLocked: user.isLocked(),
      role: {
        id: user.role.id,
        name: user.role.name,
        nameAr: user.role.name_ar
      },
      createdAt: user.created_at,
      updatedAt: user.updated_at
    }));

    res.paginated(usersData, {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count
    }, 'تم جلب المستخدمين بنجاح');

  } catch (error) {
    console.error('خطأ في جلب المستخدمين:', error);
    res.error('حدث خطأ في جلب المستخدمين', 500);
  }
};

// الحصول على مستخدم واحد
const getUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar', 'permissions']
      }]
    });

    if (!user) {
      return res.error('المستخدم غير موجود', 404);
    }

    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      fullName: user.getFullName(),
      phone: user.phone,
      avatar: user.avatar,
      isActive: user.is_active,
      emailVerified: user.email_verified,
      lastLogin: user.last_login,
      loginAttempts: user.login_attempts,
      isLocked: user.isLocked(),
      role: {
        id: user.role.id,
        name: user.role.name,
        nameAr: user.role.name_ar,
        permissions: user.role.permissions
      },
      createdAt: user.created_at,
      updatedAt: user.updated_at
    };

    res.success(userData, 'تم جلب المستخدم بنجاح');

  } catch (error) {
    console.error('خطأ في جلب المستخدم:', error);
    res.error('حدث خطأ في جلب المستخدم', 500);
  }
};

// إنشاء مستخدم جديد
const createUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const { 
      username, 
      email, 
      password, 
      firstName, 
      lastName, 
      phone, 
      roleId,
      isActive = true,
      emailVerified = false
    } = req.body;

    // التحقق من قوة كلمة المرور
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return res.error('كلمة المرور غير قوية بما فيه الكفاية', 400, passwordValidation.errors);
    }

    // التحقق من وجود الدور
    const role = await Role.findByPk(roleId);
    if (!role) {
      return res.error('الدور المحدد غير موجود', 400);
    }

    // إنشاء المستخدم
    const userData = {
      username,
      email,
      password_hash: password,
      first_name: firstName,
      last_name: lastName,
      phone,
      role_id: roleId,
      is_active: isActive,
      email_verified: emailVerified,
      created_by: req.user.id
    };

    const user = await User.createUser(userData);

    // جلب المستخدم مع بيانات الدور
    const createdUser = await User.findByPk(user.id, {
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar']
      }]
    });

    const responseData = {
      id: createdUser.id,
      username: createdUser.username,
      email: createdUser.email,
      firstName: createdUser.first_name,
      lastName: createdUser.last_name,
      fullName: createdUser.getFullName(),
      phone: createdUser.phone,
      isActive: createdUser.is_active,
      emailVerified: createdUser.email_verified,
      role: {
        id: createdUser.role.id,
        name: createdUser.role.name,
        nameAr: createdUser.role.name_ar
      },
      createdAt: createdUser.created_at
    };

    res.success(responseData, 'تم إنشاء المستخدم بنجاح', 201);

  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error);
    if (error.message.includes('مستخدم بالفعل')) {
      res.error(error.message, 400);
    } else {
      res.error('حدث خطأ في إنشاء المستخدم', 500);
    }
  }
};

// تحديث مستخدم
const updateUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const { id } = req.params;
    const { 
      username, 
      email, 
      firstName, 
      lastName, 
      phone, 
      roleId,
      isActive,
      emailVerified
    } = req.body;

    // التحقق من وجود المستخدم
    const user = await User.findByPk(id);
    if (!user) {
      return res.error('المستخدم غير موجود', 404);
    }

    // التحقق من الصلاحيات (لا يمكن للمستخدم تعديل نفسه إلا في حالات معينة)
    if (parseInt(id) === req.user.id && (isActive === false || roleId)) {
      return res.error('لا يمكنك تعديل حالة النشاط أو الدور الخاص بك', 403);
    }

    // التحقق من وجود الدور إذا تم تحديده
    if (roleId) {
      const role = await Role.findByPk(roleId);
      if (!role) {
        return res.error('الدور المحدد غير موجود', 400);
      }
    }

    // بناء بيانات التحديث
    const updateData = {};
    if (username !== undefined) updateData.username = username;
    if (email !== undefined) updateData.email = email;
    if (firstName !== undefined) updateData.first_name = firstName;
    if (lastName !== undefined) updateData.last_name = lastName;
    if (phone !== undefined) updateData.phone = phone;
    if (roleId !== undefined) updateData.role_id = roleId;
    if (isActive !== undefined) updateData.is_active = isActive;
    if (emailVerified !== undefined) updateData.email_verified = emailVerified;

    // تحديث المستخدم
    const updatedUser = await User.updateUser(id, updateData);

    // جلب المستخدم المحدث مع بيانات الدور
    const userWithRole = await User.findByPk(updatedUser.id, {
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar']
      }]
    });

    const responseData = {
      id: userWithRole.id,
      username: userWithRole.username,
      email: userWithRole.email,
      firstName: userWithRole.first_name,
      lastName: userWithRole.last_name,
      fullName: userWithRole.getFullName(),
      phone: userWithRole.phone,
      isActive: userWithRole.is_active,
      emailVerified: userWithRole.email_verified,
      role: {
        id: userWithRole.role.id,
        name: userWithRole.role.name,
        nameAr: userWithRole.role.name_ar
      },
      updatedAt: userWithRole.updated_at
    };

    res.success(responseData, 'تم تحديث المستخدم بنجاح');

  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error);
    if (error.message.includes('مستخدم بالفعل')) {
      res.error(error.message, 400);
    } else {
      res.error('حدث خطأ في تحديث المستخدم', 500);
    }
  }
};

// حذف مستخدم
const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من عدم حذف المستخدم لنفسه
    if (parseInt(id) === req.user.id) {
      return res.error('لا يمكنك حذف حسابك الخاص', 403);
    }

    const deletedUser = await User.deleteUser(id);

    res.success({
      id: deletedUser.id,
      username: deletedUser.username,
      isActive: deletedUser.is_active
    }, 'تم حذف المستخدم بنجاح');

  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error);
    if (error.message === 'المستخدم غير موجود') {
      res.error(error.message, 404);
    } else {
      res.error('حدث خطأ في حذف المستخدم', 500);
    }
  }
};

// البحث في المستخدمين
const searchUsers = async (req, res) => {
  try {
    const { q: searchTerm, limit = 10 } = req.query;

    if (!searchTerm || searchTerm.length < 2) {
      return res.error('يجب أن يكون مصطلح البحث أكثر من حرفين', 400);
    }

    const users = await User.searchUsers(searchTerm, {
      limit: parseInt(limit),
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar']
      }]
    });

    const usersData = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.getFullName(),
      role: user.role.name_ar,
      isActive: user.is_active
    }));

    res.success(usersData, 'تم البحث بنجاح');

  } catch (error) {
    console.error('خطأ في البحث:', error);
    res.error('حدث خطأ في البحث', 500);
  }
};

// الحصول على إحصائيات المستخدمين
const getUserStats = async (req, res) => {
  try {
    const stats = await User.getUserStats();
    
    // إحصائيات إضافية
    const roleStats = await Role.findAll({
      attributes: [
        'id',
        'name',
        'name_ar',
        [require('sequelize').fn('COUNT', require('sequelize').col('users.id')), 'userCount']
      ],
      include: [{
        model: User,
        as: 'users',
        attributes: [],
        where: { is_active: true },
        required: false
      }],
      group: ['Role.id'],
      raw: true
    });

    res.success({
      ...stats,
      roleDistribution: roleStats.map(role => ({
        roleId: role.id,
        roleName: role.name_ar,
        userCount: parseInt(role.userCount) || 0
      }))
    }, 'تم جلب الإحصائيات بنجاح');

  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    res.error('حدث خطأ في جلب الإحصائيات', 500);
  }
};

// إلغاء قفل المستخدم
const unlockUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);
    if (!user) {
      return res.error('المستخدم غير موجود', 404);
    }

    await user.resetLoginAttempts();

    res.success({
      id: user.id,
      username: user.username,
      isLocked: user.isLocked()
    }, 'تم إلغاء قفل المستخدم بنجاح');

  } catch (error) {
    console.error('خطأ في إلغاء قفل المستخدم:', error);
    res.error('حدث خطأ في إلغاء قفل المستخدم', 500);
  }
};

module.exports = {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  searchUsers,
  getUserStats,
  unlockUser
};
