// Header Component - نظام الوكالة التجارية المتكامل
class HeaderComponent {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.init();
    }

    getCurrentUser() {
        // محاكاة بيانات المستخدم الحالي
        return {
            name: 'admin',
            fullName: 'مدير النظام',
            role: 'admin',
            avatar: 'A'
        };
    }

    init() {
        this.createHeader();
        this.attachEventListeners();
        this.handleResponsive();
    }

    createHeader() {
        const headerHTML = `
            <header class="main-header">
                <div class="header-container">
                    <!-- Logo and Brand -->
                    <div class="header-brand">
                        <div class="brand-logo">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="brand-text">
                            <h1>نظام الوكالة التجارية</h1>
                            <span>النظام المتكامل</span>
                        </div>
                    </div>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>

                    <!-- Navigation Menu -->
                    <nav class="main-nav" id="mainNav">
                        <ul class="nav-menu">
                            <li class="nav-item">
                                <a href="dashboard.html" class="nav-link" data-page="dashboard">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span>لوحة التحكم</span>
                                </a>
                            </li>
                            
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">
                                    <i class="fas fa-users"></i>
                                    <span>إدارة العملاء</span>
                                    <i class="fas fa-chevron-down"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="customers.html">قائمة العملاء</a></li>
                                    <li><a href="customers.html#add">إضافة عميل جديد</a></li>
                                </ul>
                            </li>

                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">
                                    <i class="fas fa-truck"></i>
                                    <span>إدارة الموردين</span>
                                    <i class="fas fa-chevron-down"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="suppliers.html">قائمة الموردين</a></li>
                                    <li><a href="suppliers.html#add">إضافة مورد جديد</a></li>
                                </ul>
                            </li>

                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">
                                    <i class="fas fa-box"></i>
                                    <span>إدارة المنتجات</span>
                                    <i class="fas fa-chevron-down"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="products.html">قائمة المنتجات</a></li>
                                    <li><a href="products.html#add">إضافة منتج جديد</a></li>
                                    <li><a href="inventory.html">إدارة المخزون</a></li>
                                </ul>
                            </li>

                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>المبيعات</span>
                                    <i class="fas fa-chevron-down"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="sales.html">قائمة المبيعات</a></li>
                                    <li><a href="new-sale.html">فاتورة جديدة</a></li>
                                </ul>
                            </li>

                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">
                                    <i class="fas fa-file-invoice"></i>
                                    <span>المشتريات</span>
                                    <i class="fas fa-chevron-down"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="purchases.html">قائمة المشتريات</a></li>
                                    <li><a href="new-purchase.html">فاتورة جديدة</a></li>
                                </ul>
                            </li>

                            <li class="nav-item">
                                <a href="reports.html" class="nav-link" data-page="reports">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>التقارير</span>
                                </a>
                            </li>
                        </ul>
                    </nav>

                    <!-- User Menu -->
                    <div class="user-menu">
                        <div class="notifications">
                            <button class="notification-btn" id="notificationBtn">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                            <div class="notification-dropdown" id="notificationDropdown">
                                <div class="notification-header">
                                    <h4>الإشعارات</h4>
                                    <span class="notification-count">3 جديد</span>
                                </div>
                                <div class="notification-list">
                                    <div class="notification-item">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                        <div class="notification-content">
                                            <p>مخزون منتج "لابتوب HP" منخفض</p>
                                            <small>منذ 5 دقائق</small>
                                        </div>
                                    </div>
                                    <div class="notification-item">
                                        <i class="fas fa-shopping-cart text-success"></i>
                                        <div class="notification-content">
                                            <p>فاتورة مبيعات جديدة #1001</p>
                                            <small>منذ 15 دقيقة</small>
                                        </div>
                                    </div>
                                    <div class="notification-item">
                                        <i class="fas fa-user-plus text-info"></i>
                                        <div class="notification-content">
                                            <p>عميل جديد: أحمد محمد</p>
                                            <small>منذ 30 دقيقة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="notification-footer">
                                    <a href="#" class="view-all-notifications">عرض جميع الإشعارات</a>
                                </div>
                            </div>
                        </div>

                        <div class="user-profile dropdown">
                            <button class="user-profile-btn dropdown-toggle" id="userProfileBtn">
                                <div class="user-avatar">${this.currentUser.avatar}</div>
                                <div class="user-info">
                                    <span class="user-name">${this.currentUser.fullName}</span>
                                    <span class="user-role">${this.currentUser.role}</span>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu user-dropdown">
                                <li><a href="profile.html"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
                                <li class="divider"></li>
                                <li><a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </header>
        `;

        // إدراج الهيدر في بداية الصفحة
        document.body.insertAdjacentHTML('afterbegin', headerHTML);
        
        // إضافة الأنماط
        this.addStyles();
    }

    addStyles() {
        const styles = `
            <style>
                .main-header {
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(10px);
                    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    z-index: 1000;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                }

                .header-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 0 20px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 70px;
                }

                .header-brand {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    text-decoration: none;
                    color: inherit;
                }

                .brand-logo {
                    width: 50px;
                    height: 50px;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .brand-text h1 {
                    font-size: 1.3rem;
                    font-weight: 700;
                    color: #2c3e50;
                    margin: 0;
                    line-height: 1.2;
                }

                .brand-text span {
                    font-size: 0.85rem;
                    color: #7f8c8d;
                    display: block;
                }

                .mobile-menu-toggle {
                    display: none;
                    flex-direction: column;
                    background: none;
                    border: none;
                    cursor: pointer;
                    padding: 5px;
                    gap: 4px;
                }

                .mobile-menu-toggle span {
                    width: 25px;
                    height: 3px;
                    background: #2c3e50;
                    border-radius: 2px;
                    transition: all 0.3s ease;
                }

                .mobile-menu-toggle.active span:nth-child(1) {
                    transform: rotate(45deg) translate(6px, 6px);
                }

                .mobile-menu-toggle.active span:nth-child(2) {
                    opacity: 0;
                }

                .mobile-menu-toggle.active span:nth-child(3) {
                    transform: rotate(-45deg) translate(6px, -6px);
                }

                .main-nav {
                    flex: 1;
                    margin: 0 40px;
                }

                .nav-menu {
                    display: flex;
                    list-style: none;
                    margin: 0;
                    padding: 0;
                    gap: 5px;
                }

                .nav-item {
                    position: relative;
                }

                .nav-link {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 12px 16px;
                    color: #2c3e50;
                    text-decoration: none;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                    font-weight: 500;
                    white-space: nowrap;
                }

                .nav-link:hover,
                .nav-link.active {
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    transform: translateY(-1px);
                }

                .nav-link i {
                    font-size: 1rem;
                }

                .dropdown-menu {
                    position: absolute;
                    top: 100%;
                    right: 0;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    list-style: none;
                    margin: 0;
                    padding: 8px 0;
                    min-width: 200px;
                    opacity: 0;
                    visibility: hidden;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                    z-index: 1001;
                }

                .dropdown:hover .dropdown-menu {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(0);
                }

                .dropdown-menu li {
                    margin: 0;
                }

                .dropdown-menu a {
                    display: block;
                    padding: 10px 20px;
                    color: #2c3e50;
                    text-decoration: none;
                    transition: all 0.3s ease;
                }

                .dropdown-menu a:hover {
                    background: #f8f9fa;
                    color: #667eea;
                }

                .user-menu {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }

                .notifications {
                    position: relative;
                }

                .notification-btn {
                    position: relative;
                    background: none;
                    border: none;
                    cursor: pointer;
                    padding: 10px;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                    color: #2c3e50;
                }

                .notification-btn:hover {
                    background: #f8f9fa;
                }

                .notification-badge {
                    position: absolute;
                    top: 5px;
                    left: 5px;
                    background: #e74c3c;
                    color: white;
                    border-radius: 50%;
                    width: 18px;
                    height: 18px;
                    font-size: 0.7rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .notification-dropdown {
                    position: absolute;
                    top: 100%;
                    left: -200px;
                    width: 300px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    opacity: 0;
                    visibility: hidden;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                    z-index: 1001;
                }

                .notification-dropdown.show {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(0);
                }

                .notification-header {
                    padding: 15px 20px;
                    border-bottom: 1px solid #ecf0f1;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .notification-header h4 {
                    margin: 0;
                    color: #2c3e50;
                }

                .notification-count {
                    background: #667eea;
                    color: white;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 0.8rem;
                }

                .notification-list {
                    max-height: 300px;
                    overflow-y: auto;
                }

                .notification-item {
                    display: flex;
                    align-items: flex-start;
                    gap: 12px;
                    padding: 15px 20px;
                    border-bottom: 1px solid #f8f9fa;
                    transition: all 0.3s ease;
                }

                .notification-item:hover {
                    background: #f8f9fa;
                }

                .notification-item:last-child {
                    border-bottom: none;
                }

                .notification-content p {
                    margin: 0 0 5px 0;
                    color: #2c3e50;
                    font-size: 0.9rem;
                }

                .notification-content small {
                    color: #7f8c8d;
                    font-size: 0.8rem;
                }

                .notification-footer {
                    padding: 15px 20px;
                    border-top: 1px solid #ecf0f1;
                    text-align: center;
                }

                .view-all-notifications {
                    color: #667eea;
                    text-decoration: none;
                    font-weight: 500;
                }

                .user-profile {
                    position: relative;
                }

                .user-profile-btn {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    background: none;
                    border: none;
                    cursor: pointer;
                    padding: 8px 12px;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                }

                .user-profile-btn:hover {
                    background: #f8f9fa;
                }

                .user-avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                }

                .user-info {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                }

                .user-name {
                    font-weight: 600;
                    color: #2c3e50;
                    font-size: 0.9rem;
                }

                .user-role {
                    font-size: 0.8rem;
                    color: #7f8c8d;
                }

                .user-dropdown {
                    left: -100px;
                    min-width: 180px;
                }

                .user-dropdown .divider {
                    height: 1px;
                    background: #ecf0f1;
                    margin: 8px 0;
                }

                .user-dropdown a {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .text-warning { color: #f39c12; }
                .text-success { color: #27ae60; }
                .text-info { color: #3498db; }

                /* إضافة مساحة للمحتوى تحت الهيدر */
                body {
                    padding-top: 70px;
                }

                /* Responsive Design */
                @media (max-width: 1024px) {
                    .nav-link span {
                        display: none;
                    }
                    
                    .main-nav {
                        margin: 0 20px;
                    }
                }

                @media (max-width: 768px) {
                    .mobile-menu-toggle {
                        display: flex;
                    }

                    .main-nav {
                        position: absolute;
                        top: 100%;
                        left: 0;
                        right: 0;
                        background: white;
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                        margin: 0;
                        opacity: 0;
                        visibility: hidden;
                        transform: translateY(-10px);
                        transition: all 0.3s ease;
                    }

                    .main-nav.show {
                        opacity: 1;
                        visibility: visible;
                        transform: translateY(0);
                    }

                    .nav-menu {
                        flex-direction: column;
                        padding: 20px;
                        gap: 0;
                    }

                    .nav-link {
                        justify-content: flex-start;
                        padding: 15px 0;
                        border-bottom: 1px solid #f8f9fa;
                    }

                    .nav-link span {
                        display: inline;
                    }

                    .dropdown-menu {
                        position: static;
                        opacity: 1;
                        visibility: visible;
                        transform: none;
                        box-shadow: none;
                        background: #f8f9fa;
                        margin-right: 20px;
                        margin-top: 10px;
                        display: none;
                    }

                    .dropdown.show .dropdown-menu {
                        display: block;
                    }

                    .user-info {
                        display: none;
                    }

                    .notification-dropdown {
                        left: -250px;
                        width: 280px;
                    }
                }

                @media (max-width: 480px) {
                    .header-container {
                        padding: 0 15px;
                    }

                    .brand-text h1 {
                        font-size: 1.1rem;
                    }

                    .brand-text span {
                        font-size: 0.75rem;
                    }

                    .user-menu {
                        gap: 10px;
                    }

                    .notification-dropdown {
                        left: -220px;
                        width: 250px;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    attachEventListeners() {
        // Mobile menu toggle
        const mobileToggle = document.getElementById('mobileMenuToggle');
        const mainNav = document.getElementById('mainNav');

        if (mobileToggle && mainNav) {
            mobileToggle.addEventListener('click', () => {
                mobileToggle.classList.toggle('active');
                mainNav.classList.toggle('show');
            });
        }

        // Dropdown menus for mobile
        document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                if (window.innerWidth <= 768) {
                    e.preventDefault();
                    const dropdown = toggle.closest('.dropdown');
                    dropdown.classList.toggle('show');
                }
            });
        });

        // Notifications
        const notificationBtn = document.getElementById('notificationBtn');
        const notificationDropdown = document.getElementById('notificationDropdown');

        if (notificationBtn && notificationDropdown) {
            notificationBtn.addEventListener('click', () => {
                notificationDropdown.classList.toggle('show');
            });
        }

        // User profile dropdown
        const userProfileBtn = document.getElementById('userProfileBtn');
        const userDropdown = userProfileBtn?.nextElementSibling;

        if (userProfileBtn && userDropdown) {
            userProfileBtn.addEventListener('click', () => {
                userDropdown.classList.toggle('show');
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.notifications')) {
                notificationDropdown?.classList.remove('show');
            }
            if (!e.target.closest('.user-profile')) {
                userDropdown?.classList.remove('show');
            }
        });

        // Set active navigation item
        this.setActiveNavItem();
    }

    setActiveNavItem() {
        const currentPage = window.location.pathname.split('/').pop() || 'dashboard.html';
        const navLinks = document.querySelectorAll('.nav-link[data-page]');
        
        navLinks.forEach(link => {
            const page = link.getAttribute('data-page');
            if (currentPage.includes(page)) {
                link.classList.add('active');
            }
        });
    }

    handleResponsive() {
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                const mainNav = document.getElementById('mainNav');
                const mobileToggle = document.getElementById('mobileMenuToggle');
                
                mainNav?.classList.remove('show');
                mobileToggle?.classList.remove('active');
                
                // Reset dropdown states
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
    }
}

// Global logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('authToken');
        sessionStorage.clear();
        window.location.href = 'index.html';
    }
}

// Initialize header when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HeaderComponent();
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeaderComponent;
}
