<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المبيعات - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="components/header.js"></script>
    <script src="components/crud.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 15px;
        }

        .stat-card .today .icon { background: linear-gradient(135deg, #43e97b, #38f9d7); }
        .stat-card .month .icon { background: linear-gradient(135deg, #667eea, #764ba2); }
        .stat-card .year .icon { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .stat-card .total .icon { background: linear-gradient(135deg, #4facfe, #00f2fe); }

        .stat-card h3 {
            color: #2c3e50;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .search-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .sales-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #2c3e50;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-completed {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
        }

        .status-cancelled {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .search-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-shopping-cart"></i> إدارة المبيعات</h1>
            <div class="header-actions">
                <a href="dashboard.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                </a>
                <a href="new-sale.html" class="btn btn-success">
                    <i class="fas fa-plus"></i> فاتورة مبيعات جديدة
                </a>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="stats-row">
            <div class="stat-card today">
                <div class="icon"><i class="fas fa-calendar-day"></i></div>
                <h3>مبيعات اليوم</h3>
                <div class="number" id="todaySales">0</div>
            </div>
            
            <div class="stat-card month">
                <div class="icon"><i class="fas fa-calendar-alt"></i></div>
                <h3>مبيعات الشهر</h3>
                <div class="number" id="monthSales">0</div>
            </div>
            
            <div class="stat-card year">
                <div class="icon"><i class="fas fa-calendar"></i></div>
                <h3>مبيعات السنة</h3>
                <div class="number" id="yearSales">0</div>
            </div>
            
            <div class="stat-card total">
                <div class="icon"><i class="fas fa-chart-line"></i></div>
                <h3>إجمالي المبيعات</h3>
                <div class="number" id="totalSales">0</div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-grid">
                <div class="form-group">
                    <label>رقم الفاتورة</label>
                    <input type="text" class="form-control" id="searchInvoice" placeholder="ادخل رقم الفاتورة">
                </div>
                <div class="form-group">
                    <label>العميل</label>
                    <input type="text" class="form-control" id="searchCustomer" placeholder="ادخل اسم العميل">
                </div>
                <div class="form-group">
                    <label>من تاريخ</label>
                    <input type="date" class="form-control" id="searchFromDate">
                </div>
                <div class="form-group">
                    <label>إلى تاريخ</label>
                    <input type="date" class="form-control" id="searchToDate">
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="searchSales()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Sales Table -->
        <div class="sales-table">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...
            </div>
            
            <div id="salesContent" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المتبقي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="salesTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <!-- أزرار التصفح -->
                </div>
            </div>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-shopping-cart"></i>
                <h3>لا توجد فواتير مبيعات</h3>
                <p>لم يتم العثور على أي فواتير مبيعات. ابدأ بإنشاء فاتورة جديدة.</p>
            </div>
        </div>
    </div>

    <script>
        let sales = [];
        let currentPage = 1;
        let totalPages = 1;
        const itemsPerPage = 10;

        // تحميل المبيعات
        async function loadSales() {
            try {
                document.getElementById('loadingIndicator').style.display = 'block';
                document.getElementById('salesContent').style.display = 'none';
                document.getElementById('emptyState').style.display = 'none';

                const response = await fetch('/api/sales');
                const data = await response.json();

                if (data.success) {
                    sales = data.data || [];
                    displaySales();
                    loadSalesStats();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل المبيعات:', error);
                // عرض بيانات تجريبية
                sales = [
                    {
                        id: 1,
                        invoiceNumber: 'INV-2024-001',
                        customerName: 'أحمد محمد',
                        saleDate: '2024-01-15',
                        totalAmount: 5000,
                        paidAmount: 3000,
                        remainingAmount: 2000,
                        status: 'pending'
                    },
                    {
                        id: 2,
                        invoiceNumber: 'INV-2024-002',
                        customerName: 'فاطمة علي',
                        saleDate: '2024-01-20',
                        totalAmount: 8500,
                        paidAmount: 8500,
                        remainingAmount: 0,
                        status: 'completed'
                    }
                ];
                displaySales();
                loadSalesStats();
            }
        }

        // تحميل إحصائيات المبيعات
        function loadSalesStats() {
            // حساب الإحصائيات من البيانات المحملة
            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            let todayTotal = 0;
            let monthTotal = 0;
            let yearTotal = 0;
            let grandTotal = 0;

            sales.forEach(sale => {
                const saleDate = new Date(sale.saleDate);
                const amount = sale.totalAmount;

                grandTotal += amount;

                if (sale.saleDate === today) {
                    todayTotal += amount;
                }

                if (saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear) {
                    monthTotal += amount;
                }

                if (saleDate.getFullYear() === currentYear) {
                    yearTotal += amount;
                }
            });

            document.getElementById('todaySales').textContent = todayTotal.toLocaleString() + ' ر.س';
            document.getElementById('monthSales').textContent = monthTotal.toLocaleString() + ' ر.س';
            document.getElementById('yearSales').textContent = yearTotal.toLocaleString() + ' ر.س';
            document.getElementById('totalSales').textContent = grandTotal.toLocaleString() + ' ر.س';
        }

        // عرض المبيعات
        function displaySales() {
            const tbody = document.getElementById('salesTableBody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageSales = sales.slice(startIndex, endIndex);

            if (sales.length === 0) {
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('emptyState').style.display = 'block';
                return;
            }

            tbody.innerHTML = '';
            pageSales.forEach(sale => {
                const statusText = {
                    'completed': 'مكتملة',
                    'pending': 'معلقة',
                    'cancelled': 'ملغية'
                };

                const statusClass = {
                    'completed': 'status-completed',
                    'pending': 'status-pending',
                    'cancelled': 'status-cancelled'
                };

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: 600; color: #667eea;">${sale.invoiceNumber}</div>
                    </td>
                    <td>${sale.customerName}</td>
                    <td>${new Date(sale.saleDate).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <div style="font-weight: 600; color: #2c3e50;">${sale.totalAmount.toLocaleString()} ر.س</div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: #27ae60;">${sale.paidAmount.toLocaleString()} ر.س</div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: ${sale.remainingAmount > 0 ? '#e74c3c' : '#27ae60'};">
                            ${sale.remainingAmount.toLocaleString()} ر.س
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${statusClass[sale.status]}">
                            ${statusText[sale.status]}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-info btn-sm" onclick="viewSale(${sale.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="editSale(${sale.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteSale(${sale.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('salesContent').style.display = 'block';
            
            updatePagination();
        }

        // تحديث التصفح
        function updatePagination() {
            totalPages = Math.ceil(sales.length / itemsPerPage);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                const button = document.createElement('button');
                button.textContent = i;
                button.className = i === currentPage ? 'active' : '';
                button.onclick = () => {
                    currentPage = i;
                    displaySales();
                };
                pagination.appendChild(button);
            }
        }

        // البحث في المبيعات
        function searchSales() {
            // تطبيق فلاتر البحث
            console.log('البحث في المبيعات...');
        }

        // عرض تفاصيل المبيعة
        function viewSale(id) {
            window.location.href = `sale-details.html?id=${id}`;
        }

        // تعديل المبيعة
        function editSale(id) {
            window.location.href = `edit-sale.html?id=${id}`;
        }

        // حذف المبيعة
        function deleteSale(id) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                console.log('حذف الفاتورة:', id);
            }
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadSales();
        });
    </script>
</body>
</html>
