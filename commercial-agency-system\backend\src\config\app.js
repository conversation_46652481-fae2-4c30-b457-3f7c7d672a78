const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const path = require('path');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// إعداد التطبيق
const createApp = () => {
  const app = express();

  // إعدادات الأمان
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        manifestSrc: ["'self'"]
      }
    },
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: "cross-origin" }
  }));

  // إعدادات CORS
  const corsOptions = {
    origin: function (origin, callback) {
      const allowedOrigins = [
        process.env.CORS_ORIGIN || 'http://localhost:3000',
        'http://localhost:3001',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001'
      ];
      
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('غير مسموح بواسطة CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'Cache-Control',
      'Pragma'
    ],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count']
  };

  app.use(cors(corsOptions));

  // إعدادات معدل الطلبات
  const limiter = rateLimit({
    windowMs: (parseInt(process.env.RATE_LIMIT_WINDOW) || 15) * 60 * 1000,
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    message: {
      error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً',
      retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW) || 15) * 60)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // تجاهل التحديد للطلبات الداخلية
      return req.ip === '127.0.0.1' || req.ip === '::1';
    }
  });

  app.use('/api/', limiter);

  // إعدادات الضغط
  app.use(compression({
    filter: (req, res) => {
      if (req.headers['x-no-compression']) {
        return false;
      }
      return compression.filter(req, res);
    },
    level: 6,
    threshold: 1024
  }));

  // إعدادات السجلات
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('combined'));
  } else {
    app.use(morgan('combined', {
      skip: (req, res) => res.statusCode < 400
    }));
  }

  // إعدادات تحليل البيانات
  app.use(express.json({ 
    limit: '10mb',
    verify: (req, res, buf) => {
      req.rawBody = buf;
    }
  }));
  
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
  }));

  // إعدادات الجلسات
  app.use(session({
    secret: process.env.SESSION_SECRET || 'your-session-secret',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 ساعة
    },
    name: 'commercial.sid'
  }));

  // إعداد الملفات الثابتة
  app.use('/uploads', express.static(path.join(__dirname, '../../uploads')));
  app.use('/reports', express.static(path.join(__dirname, '../../reports')));
  app.use('/public', express.static(path.join(__dirname, '../../public')));

  // إعداد معلومات الطلب
  app.use((req, res, next) => {
    req.requestTime = new Date().toISOString();
    req.requestId = require('uuid').v4();
    
    // إضافة معلومات إضافية للطلب
    req.clientInfo = {
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      referer: req.get('Referer'),
      acceptLanguage: req.get('Accept-Language')
    };
    
    next();
  });

  // إعداد الاستجابة
  app.use((req, res, next) => {
    // إضافة دوال مساعدة للاستجابة
    res.success = (data = null, message = 'تم بنجاح', statusCode = 200) => {
      return res.status(statusCode).json({
        success: true,
        message,
        data,
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    };

    res.error = (message = 'حدث خطأ', statusCode = 500, errors = null) => {
      return res.status(statusCode).json({
        success: false,
        message,
        errors,
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    };

    res.paginated = (data, pagination, message = 'تم بنجاح') => {
      return res.status(200).json({
        success: true,
        message,
        data,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: pagination.total,
          pages: Math.ceil(pagination.total / pagination.limit)
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    };

    next();
  });

  return app;
};

// إعدادات الخادم
const SERVER_CONFIG = {
  port: process.env.PORT || 5000,
  host: process.env.HOST || 'localhost',
  environment: process.env.NODE_ENV || 'development'
};

// إعدادات قاعدة البيانات
const DATABASE_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  name: process.env.DB_NAME || 'commercial_agency_db',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || ''
};

// إعدادات الملفات
const FILE_CONFIG = {
  uploadPath: process.env.UPLOAD_PATH || './uploads',
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
  allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx').split(',')
};

// إعدادات البريد الإلكتروني
const EMAIL_CONFIG = {
  host: process.env.MAIL_HOST || 'smtp.gmail.com',
  port: process.env.MAIL_PORT || 587,
  user: process.env.MAIL_USER || '',
  password: process.env.MAIL_PASS || '',
  from: process.env.MAIL_FROM || '<EMAIL>',
  fromName: process.env.MAIL_FROM_NAME || 'Commercial Agency System'
};

// إعدادات الشركة
const COMPANY_CONFIG = {
  name: process.env.COMPANY_NAME || 'الوكالة التجارية المتكاملة',
  nameEn: process.env.COMPANY_NAME_EN || 'Integrated Commercial Agency',
  address: process.env.COMPANY_ADDRESS || 'الرياض، المملكة العربية السعودية',
  phone: process.env.COMPANY_PHONE || '+966-11-1234567',
  email: process.env.COMPANY_EMAIL || '<EMAIL>',
  website: process.env.COMPANY_WEBSITE || 'https://commercial-agency.com',
  taxNumber: process.env.TAX_NUMBER || '123456789012345',
  commercialRegister: process.env.COMMERCIAL_REGISTER || '1010123456'
};

// إعدادات النظام
const SYSTEM_CONFIG = {
  defaultCurrency: process.env.DEFAULT_CURRENCY || 'SAR',
  defaultTaxRate: parseFloat(process.env.DEFAULT_TAX_RATE) || 15,
  defaultLanguage: process.env.DEFAULT_LANGUAGE || 'ar',
  timezone: process.env.TIMEZONE || 'Asia/Riyadh',
  dateFormat: process.env.DATE_FORMAT || 'DD/MM/YYYY',
  timeFormat: process.env.TIME_FORMAT || 'HH:mm'
};

module.exports = {
  createApp,
  SERVER_CONFIG,
  DATABASE_CONFIG,
  FILE_CONFIG,
  EMAIL_CONFIG,
  COMPANY_CONFIG,
  SYSTEM_CONFIG
};
