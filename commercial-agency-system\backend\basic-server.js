const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = process.env.PORT || 3001;

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        resolve({});
      }
    });
    req.on('error', reject);
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': 'http://localhost:3000',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'true'
  });
  res.end(JSON.stringify(data));
}

// Create server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': 'http://localhost:3000',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true'
    });
    res.end();
    return;
  }

  try {
    // Health check
    if (path === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'OK',
        message: 'Server is running',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // API test
    if (path === '/api/test' && method === 'GET') {
      sendJSON(res, 200, {
        message: 'API is working!',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Login endpoint
    if (path === '/api/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { username, password } = body;

      if (username === 'admin' && password === 'Admin@123456') {
        sendJSON(res, 200, {
          success: true,
          data: {
            user: {
              id: 1,
              username: 'admin',
              email: '<EMAIL>',
              firstName: 'مدير',
              lastName: 'النظام',
              fullName: 'مدير النظام',
              role: {
                id: 1,
                name: 'admin',
                nameAr: 'مدير النظام',
                permissions: ['admin.all']
              }
            },
            accessToken: 'mock-jwt-token-' + Date.now()
          }
        });
      } else {
        sendJSON(res, 401, {
          success: false,
          message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
        });
      }
      return;
    }

    // Auth check
    if (path === '/api/auth/me' && method === 'GET') {
      const authHeader = req.headers.authorization;

      if (authHeader && authHeader.startsWith('Bearer ')) {
        sendJSON(res, 200, {
          success: true,
          data: {
            user: {
              id: 1,
              username: 'admin',
              email: '<EMAIL>',
              firstName: 'مدير',
              lastName: 'النظام',
              fullName: 'مدير النظام',
              role: {
                id: 1,
                name: 'admin',
                nameAr: 'مدير النظام',
                permissions: ['admin.all']
              }
            }
          }
        });
      } else {
        sendJSON(res, 401, {
          success: false,
          message: 'غير مصرح له'
        });
      }
      return;
    }

    // Logout
    if (path === '/api/auth/logout' && method === 'POST') {
      sendJSON(res, 200, {
        success: true,
        message: 'تم تسجيل الخروج بنجاح'
      });
      return;
    }

    // 404 for other routes
    sendJSON(res, 404, {
      success: false,
      message: 'المسار غير موجود'
    });

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// Start server
server.listen(PORT, 'localhost', () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🏥 فحص الصحة متاح على: http://localhost:${PORT}/health`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
});

// Handle server errors
server.on('error', (error) => {
  console.error('خطأ في الخادم:', error);
});

module.exports = server;
