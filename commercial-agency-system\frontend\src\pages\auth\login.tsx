import React, { useState } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  FormControlLabel,
  Checkbox,
  Alert,
  InputAdornment,
  IconButton,
  Divider,
  Container,
  Paper,
  Grid,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person,
  Lock,
  Login as LoginIcon,
  Business,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-hot-toast';

import { useAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/common/LoadingScreen';

// تصميم الحاوية الرئيسية
const LoginContainer = styled(Container)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
  padding: theme.spacing(2),
}));

// تصميم بطاقة تسجيل الدخول
const LoginCard = styled(Card)(({ theme }) => ({
  maxWidth: 450,
  width: '100%',
  boxShadow: theme.shadows[10],
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
}));

// تصميم الهيدر
const LoginHeader = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: theme.palette.primary.contrastText,
  padding: theme.spacing(3),
  textAlign: 'center',
}));

// تصميم الشعار
const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
}));

// تصميم أيقونة الشعار
const LogoIcon = styled(Business)(({ theme }) => ({
  fontSize: 48,
  marginRight: theme.spacing(1),
}));

// مخطط التحقق من صحة البيانات
const validationSchema = yup.object({
  username: yup
    .string()
    .required('اسم المستخدم مطلوب')
    .min(3, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل'),
  password: yup
    .string()
    .required('كلمة المرور مطلوبة')
    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  rememberMe: yup.boolean(),
});

// نوع بيانات النموذج
type LoginFormData = {
  username: string;
  password: string;
  rememberMe: boolean;
};

const LoginPage: NextPage = () => {
  const router = useRouter();
  const { login, isLoading, error, clearAuthError } = useAuth();
  const [showPassword, setShowPassword] = useState(false);

  // إعداد النموذج
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<LoginFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  });

  // دالة إرسال النموذج
  const onSubmit = async (data: LoginFormData) => {
    try {
      clearAuthError();
      await login(data);
      toast.success('تم تسجيل الدخول بنجاح');
    } catch (error: any) {
      console.error('خطأ في تسجيل الدخول:', error);
      
      // معالجة أخطاء محددة
      if (error.message?.includes('اسم المستخدم')) {
        setError('username', { message: error.message });
      } else if (error.message?.includes('كلمة المرور')) {
        setError('password', { message: error.message });
      } else {
        toast.error(error.message || 'فشل في تسجيل الدخول');
      }
    }
  };

  // دالة تبديل إظهار كلمة المرور
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // إذا كان التحميل جاري
  if (isLoading) {
    return <LoadingScreen message="جاري تسجيل الدخول..." />;
  }

  return (
    <>
      <Head>
        <title>تسجيل الدخول - نظام الوكالة التجارية المتكامل</title>
        <meta name="description" content="تسجيل الدخول إلى نظام الوكالة التجارية المتكامل" />
      </Head>

      <LoginContainer maxWidth={false}>
        <LoginCard>
          <LoginHeader>
            <LogoContainer>
              <LogoIcon />
              <Box>
                <Typography variant="h5" component="h1" fontWeight="bold">
                  الوكالة التجارية
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  نظام إدارة متكامل
                </Typography>
              </Box>
            </LogoContainer>
          </LoginHeader>

          <CardContent sx={{ p: 4 }}>
            <Typography variant="h6" component="h2" gutterBottom textAlign="center">
              تسجيل الدخول
            </Typography>

            <Typography variant="body2" color="text.secondary" textAlign="center" mb={3}>
              أدخل بيانات تسجيل الدخول للوصول إلى النظام
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              <Controller
                name="username"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="اسم المستخدم"
                    placeholder="أدخل اسم المستخدم"
                    error={!!errors.username}
                    helperText={errors.username?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person color="action" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{ mb: 2 }}
                    autoComplete="username"
                    autoFocus
                  />
                )}
              />

              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type={showPassword ? 'text' : 'password'}
                    label="كلمة المرور"
                    placeholder="أدخل كلمة المرور"
                    error={!!errors.password}
                    helperText={errors.password?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={togglePasswordVisibility}
                            edge="end"
                            aria-label="toggle password visibility"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{ mb: 2 }}
                    autoComplete="current-password"
                  />
                )}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Controller
                  name="rememberMe"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox {...field} checked={field.value} />}
                      label="تذكرني"
                    />
                  )}
                />

                <Link href="/auth/forgot-password" passHref>
                  <Typography
                    component="a"
                    variant="body2"
                    color="primary"
                    sx={{ textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                  >
                    نسيت كلمة المرور؟
                  </Typography>
                </Link>
              </Box>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={isSubmitting}
                startIcon={<LoginIcon />}
                sx={{ mb: 2, py: 1.5 }}
              >
                {isSubmitting ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
              </Button>
            </Box>

            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                أو
              </Typography>
            </Divider>

            <Box textAlign="center">
              <Typography variant="body2" color="text.secondary">
                ليس لديك حساب؟{' '}
                <Link href="/auth/register" passHref>
                  <Typography
                    component="a"
                    color="primary"
                    sx={{ textDecoration: 'none', fontWeight: 'medium', '&:hover': { textDecoration: 'underline' } }}
                  >
                    إنشاء حساب جديد
                  </Typography>
                </Link>
              </Typography>
            </Box>
          </CardContent>

          {/* معلومات إضافية */}
          <Box sx={{ p: 2, bgcolor: 'grey.50', textAlign: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              بيانات تسجيل الدخول الافتراضية:
              <br />
              اسم المستخدم: admin | كلمة المرور: Admin@123456
            </Typography>
          </Box>
        </LoginCard>

        {/* معلومات النظام */}
        <Paper
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            p: 2,
            maxWidth: 300,
            display: { xs: 'none', md: 'block' },
          }}
        >
          <Typography variant="h6" gutterBottom>
            مميزات النظام
          </Typography>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <Typography variant="body2">• إدارة شاملة للعملاء والموردين</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2">• تتبع المخزون والمبيعات</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2">• تقارير مالية متقدمة</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2">• واجهة سهلة الاستخدام</Typography>
            </Grid>
          </Grid>
        </Paper>
      </LoginContainer>
    </>
  );
};

export default LoginPage;
