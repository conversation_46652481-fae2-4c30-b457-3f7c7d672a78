const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// نموذج المخزون
const Inventory = sequelize.define('Inventory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'product_id'
  },
  warehouse_id: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    field: 'warehouse_id'
  },
  quantity_on_hand: {
    type: DataTypes.DECIMAL(15, 3),
    defaultValue: 0,
    field: 'quantity_on_hand'
  },
  quantity_reserved: {
    type: DataTypes.DECIMAL(15, 3),
    defaultValue: 0,
    field: 'quantity_reserved'
  },
  quantity_available: {
    type: DataTypes.VIRTUAL,
    get() {
      return parseFloat(this.quantity_on_hand) - parseFloat(this.quantity_reserved);
    }
  },
  last_purchase_price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    field: 'last_purchase_price'
  },
  average_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    field: 'average_cost'
  },
  last_updated: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'last_updated'
  }
}, {
  tableName: 'inventory',
  timestamps: false,
  indexes: [
    {
      unique: true,
      fields: ['product_id', 'warehouse_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['warehouse_id']
    }
  ]
});

// نموذج حركات المخزون
const InventoryMovement = sequelize.define('InventoryMovement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'product_id'
  },
  warehouse_id: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    field: 'warehouse_id'
  },
  movement_type: {
    type: DataTypes.ENUM('in', 'out', 'adjustment', 'transfer'),
    allowNull: false,
    field: 'movement_type'
  },
  reference_type: {
    type: DataTypes.ENUM('purchase', 'sale', 'adjustment', 'transfer', 'return', 'opening_balance'),
    allowNull: false,
    field: 'reference_type'
  },
  reference_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'reference_id'
  },
  quantity: {
    type: DataTypes.DECIMAL(15, 3),
    allowNull: false
  },
  unit_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    field: 'unit_cost'
  },
  total_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    field: 'total_cost'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  }
}, {
  tableName: 'inventory_movements',
  timestamps: false,
  indexes: [
    {
      fields: ['product_id']
    },
    {
      fields: ['warehouse_id']
    },
    {
      fields: ['reference_type', 'reference_id']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['created_by']
    }
  ]
});

// الدوال الإضافية لنموذج المخزون
Inventory.prototype.isLowStock = function() {
  // سيتم الحصول على معلومات المنتج للمقارنة مع reorder_level
  return false; // مؤقت
};

Inventory.prototype.isOutOfStock = function() {
  return parseFloat(this.quantity_available) <= 0;
};

Inventory.prototype.getStockStatus = function() {
  const available = parseFloat(this.quantity_available);
  if (available <= 0) return 'out_of_stock';
  if (this.isLowStock()) return 'low_stock';
  return 'in_stock';
};

Inventory.prototype.getStockValue = function() {
  const cost = parseFloat(this.average_cost) || parseFloat(this.last_purchase_price) || 0;
  return parseFloat(this.quantity_on_hand) * cost;
};

// الدوال الثابتة لنموذج المخزون
Inventory.findByProduct = async function(productId, warehouseId = 1) {
  return await this.findOne({
    where: { product_id: productId, warehouse_id: warehouseId }
  });
};

Inventory.createOrUpdate = async function(productId, warehouseId, data) {
  const [inventory, created] = await this.findOrCreate({
    where: { product_id: productId, warehouse_id: warehouseId },
    defaults: {
      product_id: productId,
      warehouse_id: warehouseId,
      ...data
    }
  });
  
  if (!created) {
    await inventory.update(data);
  }
  
  return inventory;
};

Inventory.updateQuantity = async function(productId, warehouseId, quantity, movementType, referenceType, referenceId, unitCost, userId, notes) {
  const transaction = await sequelize.transaction();
  
  try {
    // الحصول على المخزون الحالي أو إنشاؤه
    let inventory = await this.findByProduct(productId, warehouseId);
    if (!inventory) {
      inventory = await this.create({
        product_id: productId,
        warehouse_id: warehouseId,
        quantity_on_hand: 0,
        quantity_reserved: 0
      }, { transaction });
    }
    
    // حساب الكمية الجديدة
    let newQuantity = parseFloat(inventory.quantity_on_hand);
    if (movementType === 'in') {
      newQuantity += parseFloat(quantity);
    } else if (movementType === 'out') {
      newQuantity -= parseFloat(quantity);
    } else if (movementType === 'adjustment') {
      newQuantity = parseFloat(quantity);
    }
    
    // التحقق من عدم وجود كمية سالبة
    if (newQuantity < 0) {
      throw new Error('الكمية المتاحة غير كافية');
    }
    
    // حساب متوسط التكلفة للمشتريات
    let newAverageCost = inventory.average_cost;
    if (movementType === 'in' && unitCost && unitCost > 0) {
      const currentValue = parseFloat(inventory.quantity_on_hand) * parseFloat(inventory.average_cost || 0);
      const newValue = parseFloat(quantity) * parseFloat(unitCost);
      const totalQuantity = parseFloat(inventory.quantity_on_hand) + parseFloat(quantity);
      
      if (totalQuantity > 0) {
        newAverageCost = (currentValue + newValue) / totalQuantity;
      }
    }
    
    // تحديث المخزون
    await inventory.update({
      quantity_on_hand: newQuantity,
      last_purchase_price: unitCost || inventory.last_purchase_price,
      average_cost: newAverageCost,
      last_updated: new Date()
    }, { transaction });
    
    // إضافة حركة المخزون
    await InventoryMovement.create({
      product_id: productId,
      warehouse_id: warehouseId,
      movement_type: movementType,
      reference_type: referenceType,
      reference_id: referenceId,
      quantity: quantity,
      unit_cost: unitCost,
      total_cost: unitCost ? parseFloat(quantity) * parseFloat(unitCost) : null,
      notes: notes,
      created_by: userId
    }, { transaction });
    
    await transaction.commit();
    return inventory;
    
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

Inventory.reserveQuantity = async function(productId, warehouseId, quantity) {
  const inventory = await this.findByProduct(productId, warehouseId);
  if (!inventory) {
    throw new Error('المنتج غير موجود في المخزون');
  }
  
  const availableQuantity = parseFloat(inventory.quantity_on_hand) - parseFloat(inventory.quantity_reserved);
  if (availableQuantity < parseFloat(quantity)) {
    throw new Error('الكمية المتاحة غير كافية للحجز');
  }
  
  await inventory.update({
    quantity_reserved: parseFloat(inventory.quantity_reserved) + parseFloat(quantity)
  });
  
  return inventory;
};

Inventory.releaseReservedQuantity = async function(productId, warehouseId, quantity) {
  const inventory = await this.findByProduct(productId, warehouseId);
  if (!inventory) {
    throw new Error('المنتج غير موجود في المخزون');
  }
  
  const newReservedQuantity = Math.max(0, parseFloat(inventory.quantity_reserved) - parseFloat(quantity));
  
  await inventory.update({
    quantity_reserved: newReservedQuantity
  });
  
  return inventory;
};

Inventory.getLowStockProducts = async function(warehouseId = null) {
  const Product = require('./Product');
  
  const whereClause = { is_active: true };
  if (warehouseId) {
    whereClause.warehouse_id = warehouseId;
  }
  
  return await this.findAll({
    where: whereClause,
    include: [{
      model: Product,
      where: { is_active: true },
      required: true
    }],
    having: sequelize.where(
      sequelize.col('quantity_on_hand'),
      '<=',
      sequelize.col('Product.reorder_level')
    )
  });
};

Inventory.getOutOfStockProducts = async function(warehouseId = null) {
  const whereClause = { quantity_on_hand: 0 };
  if (warehouseId) {
    whereClause.warehouse_id = warehouseId;
  }
  
  return await this.findAll({
    where: whereClause,
    include: [{
      model: require('./Product'),
      where: { is_active: true },
      required: true
    }]
  });
};

Inventory.getInventoryValue = async function(warehouseId = null) {
  const whereClause = {};
  if (warehouseId) {
    whereClause.warehouse_id = warehouseId;
  }
  
  const result = await this.findAll({
    where: whereClause,
    attributes: [
      [sequelize.fn('SUM', 
        sequelize.literal('quantity_on_hand * COALESCE(average_cost, last_purchase_price, 0)')
      ), 'total_value']
    ]
  });
  
  return parseFloat(result[0].dataValues.total_value) || 0;
};

// الدوال الثابتة لنموذج حركات المخزون
InventoryMovement.getMovementsByProduct = async function(productId, options = {}) {
  return await this.findAll({
    where: { product_id: productId },
    order: [['created_at', 'DESC']],
    ...options
  });
};

InventoryMovement.getMovementsByDateRange = async function(startDate, endDate, options = {}) {
  const { Op } = require('sequelize');
  
  return await this.findAll({
    where: {
      created_at: {
        [Op.between]: [startDate, endDate]
      }
    },
    order: [['created_at', 'DESC']],
    ...options
  });
};

InventoryMovement.getMovementStats = async function(startDate, endDate) {
  const { Op } = require('sequelize');
  
  const whereClause = {};
  if (startDate && endDate) {
    whereClause.created_at = {
      [Op.between]: [startDate, endDate]
    };
  }
  
  const results = await this.findAll({
    where: whereClause,
    attributes: [
      'movement_type',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.col('quantity')), 'total_quantity']
    ],
    group: ['movement_type']
  });
  
  return results.map(result => ({
    type: result.movement_type,
    count: parseInt(result.dataValues.count),
    totalQuantity: parseFloat(result.dataValues.total_quantity)
  }));
};

// تصدير النماذج
module.exports = {
  Inventory,
  InventoryMovement
};
