# نظام الوكالة التجارية المتكامل
## Integrated Commercial Agency System

نظام إدارة شامل للوكالات التجارية يشمل إدارة العملاء والموردين والمخزون والمبيعات والمشتريات والمديونية مع واجهات محسنة وتجربة مستخدم متميزة.

## 🌟 المميزات الرئيسية

### 📊 إدارة شاملة
- **إدارة المستخدمين والصلاحيات**: نظام أدوار متقدم مع صلاحيات مفصلة
- **إدارة العملاء**: تتبع شامل للعملاء الأفراد والشركات مع إدارة الائتمان
- **إدارة الموردين**: متابعة الموردين المحليين والدوليين مع نظام تقييم
- **إدارة المنتجات**: كتالوج منتجات متكامل مع الباركود والمواصفات
- **إدارة المخزون**: تتبع دقيق للمخزون مع تنبيهات المخزون المنخفض

### 💼 العمليات التجارية
- **عمليات الشراء**: طلبات شراء وفواتير مع تتبع التسليم
- **عمليات البيع**: عروض أسعار وفواتير بيع مع خصومات مرنة
- **إدارة المدفوعات**: تتبع المدفوعات والمديونية مع تقارير الاستحقاق
- **إدارة المخازن**: عدة مخازن مع نقل المخزون

### 📈 التقارير والتحليلات
- **تقارير المبيعات**: تحليل شامل للمبيعات والأرباح
- **تقارير المشتريات**: متابعة المشتريات والموردين
- **تقارير المخزون**: حالة المخزون وحركاته
- **التقارير المالية**: الأرباح والخسائر والتدفق النقدي

### 🔒 الأمان والموثوقية
- **مصادقة متقدمة**: JWT مع Refresh Tokens
- **تشفير البيانات**: حماية كاملة للبيانات الحساسة
- **سجل العمليات**: تتبع جميع العمليات والتغييرات
- **نسخ احتياطية**: نظام نسخ احتياطي تلقائي

## 🛠️ التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الويب
- **MySQL** - قاعدة البيانات الرئيسية
- **Sequelize** - ORM لقاعدة البيانات
- **JWT** - المصادقة والتفويض
- **Joi** - التحقق من صحة البيانات

### Frontend (قادم)
- **React.js** - مكتبة واجهة المستخدم
- **Next.js** - إطار عمل React
- **Material-UI** - مكونات واجهة المستخدم
- **Redux Toolkit** - إدارة الحالة
- **Chart.js** - الرسوم البيانية

## 📋 متطلبات النظام

- **Node.js** >= 16.0.0
- **MySQL** >= 8.0
- **npm** >= 8.0.0

## 🚀 التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/commercial-agency/system.git
cd commercial-agency-system
```

### 2. إعداد Backend
```bash
cd backend
npm install
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p < ../database/schema.sql

# تشغيل إعدادات قاعدة البيانات
mysql -u root -p < ../database/setup.sql
```

### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتعديل ملف .env بالإعدادات المناسبة
```

### 5. مزامنة قاعدة البيانات
```bash
npm run db:migrate
```

### 6. إنشاء البيانات الأساسية
```bash
npm run db:seed
```

### 7. تشغيل الخادم
```bash
# بيئة التطوير
npm run dev

# بيئة الإنتاج
npm start
```

## 📚 الاستخدام

### تسجيل الدخول الافتراضي
- **اسم المستخدم**: admin
- **كلمة المرور**: Admin@123456

### واجهة برمجة التطبيقات
- **الخادم المحلي**: http://localhost:5000
- **التوثيق**: http://localhost:5000/api-docs
- **فحص الصحة**: http://localhost:5000/health

## 🔧 الأوامر المتاحة

```bash
# تشغيل الخادم في بيئة التطوير
npm run dev

# تشغيل الخادم في بيئة الإنتاج
npm start

# مزامنة قاعدة البيانات
npm run db:migrate

# إنشاء البيانات الأساسية
npm run db:seed

# إعادة تعيين قاعدة البيانات
npm run db:reset

# تشغيل الاختبارات
npm test

# فحص الكود
npm run lint

# إصلاح مشاكل الكود
npm run lint:fix
```

## 📁 هيكل المشروع

```
commercial-agency-system/
├── backend/                 # الخادم الخلفي
│   ├── src/
│   │   ├── config/         # إعدادات النظام
│   │   ├── controllers/    # متحكمات API
│   │   ├── models/         # نماذج قاعدة البيانات
│   │   ├── routes/         # طرق API
│   │   ├── middleware/     # الوسطاء
│   │   ├── services/       # الخدمات
│   │   ├── utils/          # الأدوات المساعدة
│   │   └── scripts/        # البرامج النصية
│   ├── uploads/            # الملفات المرفوعة
│   ├── reports/            # التقارير المولدة
│   ├── logs/               # ملفات السجلات
│   └── backups/            # النسخ الاحتياطية
├── frontend/               # الواجهة الأمامية (قادم)
├── database/               # ملفات قاعدة البيانات
└── docs/                   # التوثيق
```

## 🔐 الأدوار والصلاحيات

### الأدوار الافتراضية
1. **مدير النظام** - صلاحيات كاملة
2. **مدير المبيعات** - إدارة العملاء والمبيعات
3. **مدير المشتريات** - إدارة الموردين والمشتريات
4. **المحاسب** - إدارة المالية والمديونية
5. **موظف المبيعات** - عمليات البيع فقط
6. **أمين المخزن** - إدارة المخزون

## 📊 قاعدة البيانات

### الجداول الرئيسية
- `users` - المستخدمين
- `roles` - الأدوار والصلاحيات
- `customers` - العملاء
- `suppliers` - الموردين
- `products` - المنتجات
- `inventory` - المخزون
- `sales_invoices` - فواتير البيع
- `purchase_invoices` - فواتير الشراء
- `payments` - المدفوعات

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npm test -- --grep "User"

# تشغيل الاختبارات مع التغطية
npm run test:coverage
```

## 📈 المراقبة والأداء

- **مراقبة الصحة**: `/health`
- **إحصائيات النظام**: `/api/system/stats`
- **سجلات النظام**: مجلد `logs/`
- **مراقبة الأداء**: مدمجة في النظام

## 🔄 النسخ الاحتياطي

```bash
# إنشاء نسخة احتياطية يدوية
mysqldump -u username -p commercial_agency_db > backup.sql

# استعادة من نسخة احتياطية
mysql -u username -p commercial_agency_db < backup.sql
```

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://commercial-agency.com
- **التوثيق**: https://docs.commercial-agency.com

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام المتكامل.

---

**نظام الوكالة التجارية المتكامل** - حل شامل لإدارة الأعمال التجارية 🚀
