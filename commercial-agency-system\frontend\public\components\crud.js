// CRUD Operations Library - مكتبة العمليات الأساسية
class CRUDManager {
    constructor(config) {
        this.config = {
            apiBaseUrl: '/api',
            itemsPerPage: 10,
            ...config
        };
        this.currentPage = 1;
        this.totalPages = 1;
        this.data = [];
        this.filteredData = [];
        this.searchFilters = {};
        this.sortConfig = { field: null, direction: 'asc' };
    }

    // ==================== CREATE OPERATIONS ====================
    
    async create(endpoint, data, options = {}) {
        try {
            this.showLoading('جاري إضافة البيانات...');
            
            const url = `${this.config.apiBaseUrl}/${endpoint}`;
            const requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: JSON.stringify(data)
            };

            // Handle file uploads
            if (options.hasFiles) {
                const formData = new FormData();
                Object.keys(data).forEach(key => {
                    if (data[key] instanceof File) {
                        formData.append(key, data[key]);
                    } else {
                        formData.append(key, data[key]);
                    }
                });
                requestOptions.body = formData;
                delete requestOptions.headers['Content-Type'];
            }

            const response = await fetch(url, requestOptions);
            const result = await response.json();

            this.hideLoading();

            if (result.success) {
                this.showNotification('تم إضافة البيانات بنجاح', 'success');
                if (options.onSuccess) options.onSuccess(result.data);
                return result.data;
            } else {
                throw new Error(result.message || 'حدث خطأ في إضافة البيانات');
            }
        } catch (error) {
            this.hideLoading();
            this.showNotification(error.message, 'error');
            if (options.onError) options.onError(error);
            throw error;
        }
    }

    // ==================== READ OPERATIONS ====================
    
    async read(endpoint, options = {}) {
        try {
            this.showLoading('جاري تحميل البيانات...');
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.config.itemsPerPage,
                ...this.searchFilters,
                ...options.params
            });

            if (this.sortConfig.field) {
                params.append('sortBy', this.sortConfig.field);
                params.append('sortOrder', this.sortConfig.direction);
            }

            const url = `${this.config.apiBaseUrl}/${endpoint}?${params}`;
            const response = await fetch(url, {
                headers: options.headers || {}
            });
            
            const result = await response.json();
            this.hideLoading();

            if (result.success) {
                this.data = result.data || [];
                this.filteredData = [...this.data];
                this.totalPages = Math.ceil((result.total || this.data.length) / this.config.itemsPerPage);
                
                if (options.onSuccess) options.onSuccess(this.data);
                return this.data;
            } else {
                throw new Error(result.message || 'حدث خطأ في تحميل البيانات');
            }
        } catch (error) {
            this.hideLoading();
            this.showNotification(error.message, 'error');
            if (options.onError) options.onError(error);
            
            // Return mock data for development
            if (options.mockData) {
                this.data = options.mockData;
                this.filteredData = [...this.data];
                this.totalPages = Math.ceil(this.data.length / this.config.itemsPerPage);
                return this.data;
            }
            throw error;
        }
    }

    async readById(endpoint, id, options = {}) {
        try {
            this.showLoading('جاري تحميل البيانات...');
            
            const url = `${this.config.apiBaseUrl}/${endpoint}/${id}`;
            const response = await fetch(url, {
                headers: options.headers || {}
            });
            
            const result = await response.json();
            this.hideLoading();

            if (result.success) {
                if (options.onSuccess) options.onSuccess(result.data);
                return result.data;
            } else {
                throw new Error(result.message || 'حدث خطأ في تحميل البيانات');
            }
        } catch (error) {
            this.hideLoading();
            this.showNotification(error.message, 'error');
            if (options.onError) options.onError(error);
            throw error;
        }
    }

    // ==================== UPDATE OPERATIONS ====================
    
    async update(endpoint, id, data, options = {}) {
        try {
            this.showLoading('جاري تحديث البيانات...');
            
            const url = `${this.config.apiBaseUrl}/${endpoint}/${id}`;
            const requestOptions = {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: JSON.stringify(data)
            };

            // Handle file uploads
            if (options.hasFiles) {
                const formData = new FormData();
                Object.keys(data).forEach(key => {
                    if (data[key] instanceof File) {
                        formData.append(key, data[key]);
                    } else {
                        formData.append(key, data[key]);
                    }
                });
                requestOptions.body = formData;
                delete requestOptions.headers['Content-Type'];
            }

            const response = await fetch(url, requestOptions);
            const result = await response.json();

            this.hideLoading();

            if (result.success) {
                this.showNotification('تم تحديث البيانات بنجاح', 'success');
                
                // Update local data
                const index = this.data.findIndex(item => item.id == id);
                if (index !== -1) {
                    this.data[index] = { ...this.data[index], ...result.data };
                    this.filteredData = [...this.data];
                }
                
                if (options.onSuccess) options.onSuccess(result.data);
                return result.data;
            } else {
                throw new Error(result.message || 'حدث خطأ في تحديث البيانات');
            }
        } catch (error) {
            this.hideLoading();
            this.showNotification(error.message, 'error');
            if (options.onError) options.onError(error);
            throw error;
        }
    }

    // ==================== DELETE OPERATIONS ====================
    
    async delete(endpoint, id, options = {}) {
        try {
            const confirmMessage = options.confirmMessage || 'هل أنت متأكد من حذف هذا العنصر؟';
            if (!confirm(confirmMessage)) {
                return false;
            }

            this.showLoading('جاري حذف البيانات...');
            
            const url = `${this.config.apiBaseUrl}/${endpoint}/${id}`;
            const response = await fetch(url, {
                method: 'DELETE',
                headers: options.headers || {}
            });
            
            const result = await response.json();
            this.hideLoading();

            if (result.success) {
                this.showNotification('تم حذف البيانات بنجاح', 'success');
                
                // Remove from local data
                this.data = this.data.filter(item => item.id != id);
                this.filteredData = [...this.data];
                
                if (options.onSuccess) options.onSuccess(id);
                return true;
            } else {
                throw new Error(result.message || 'حدث خطأ في حذف البيانات');
            }
        } catch (error) {
            this.hideLoading();
            this.showNotification(error.message, 'error');
            if (options.onError) options.onError(error);
            throw error;
        }
    }

    // ==================== SEARCH & FILTER OPERATIONS ====================
    
    search(filters) {
        this.searchFilters = { ...filters };
        this.currentPage = 1;
        this.applyFilters();
    }

    applyFilters() {
        this.filteredData = this.data.filter(item => {
            return Object.keys(this.searchFilters).every(key => {
                const filterValue = this.searchFilters[key];
                if (!filterValue) return true;
                
                const itemValue = item[key];
                if (typeof itemValue === 'string') {
                    return itemValue.toLowerCase().includes(filterValue.toLowerCase());
                }
                return itemValue == filterValue;
            });
        });
        
        this.totalPages = Math.ceil(this.filteredData.length / this.config.itemsPerPage);
        this.displayData();
    }

    sort(field, direction = null) {
        if (this.sortConfig.field === field && !direction) {
            this.sortConfig.direction = this.sortConfig.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortConfig.field = field;
            this.sortConfig.direction = direction || 'asc';
        }

        this.filteredData.sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];
            
            // Handle different data types
            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }
            
            if (aVal < bVal) return this.sortConfig.direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortConfig.direction === 'asc' ? 1 : -1;
            return 0;
        });

        this.displayData();
    }

    // ==================== PAGINATION ====================
    
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.displayData();
        }
    }

    nextPage() {
        this.goToPage(this.currentPage + 1);
    }

    prevPage() {
        this.goToPage(this.currentPage - 1);
    }

    // ==================== DISPLAY OPERATIONS ====================
    
    displayData() {
        const startIndex = (this.currentPage - 1) * this.config.itemsPerPage;
        const endIndex = startIndex + this.config.itemsPerPage;
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        if (this.config.onDisplayData) {
            this.config.onDisplayData(pageData);
        }
        
        this.updatePagination();
    }

    updatePagination() {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        let paginationHTML = '';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button onclick="crudManager.prevPage()" class="pagination-btn">السابق</button>`;
        }

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            paginationHTML += `<button onclick="crudManager.goToPage(${i})" class="pagination-btn ${activeClass}">${i}</button>`;
        }

        // Next button
        if (this.currentPage < this.totalPages) {
            paginationHTML += `<button onclick="crudManager.nextPage()" class="pagination-btn">التالي</button>`;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    // ==================== UI HELPERS ====================
    
    showLoading(message = 'جاري التحميل...') {
        let loadingEl = document.getElementById('loading-overlay');
        if (!loadingEl) {
            loadingEl = document.createElement('div');
            loadingEl.id = 'loading-overlay';
            loadingEl.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <p class="loading-message">${message}</p>
                </div>
            `;
            document.body.appendChild(loadingEl);
            
            // Add loading styles
            this.addLoadingStyles();
        } else {
            loadingEl.querySelector('.loading-message').textContent = message;
        }
        loadingEl.style.display = 'flex';
    }

    hideLoading() {
        const loadingEl = document.getElementById('loading-overlay');
        if (loadingEl) {
            loadingEl.style.display = 'none';
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to container or create one
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            document.body.appendChild(container);
            this.addNotificationStyles();
        }

        container.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    addLoadingStyles() {
        if (document.getElementById('loading-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'loading-styles';
        styles.textContent = `
            #loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
            }

            .loading-content {
                background: white;
                padding: 30px;
                border-radius: 12px;
                text-align: center;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .loading-message {
                color: #2c3e50;
                font-weight: 500;
                margin: 0;
            }
        `;
        document.head.appendChild(styles);
    }

    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            #notification-container {
                position: fixed;
                top: 90px;
                left: 20px;
                z-index: 9998;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .notification {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                padding: 15px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                min-width: 300px;
                max-width: 400px;
                animation: slideIn 0.3s ease;
            }

            .notification-success { border-right: 4px solid #27ae60; }
            .notification-error { border-right: 4px solid #e74c3c; }
            .notification-warning { border-right: 4px solid #f39c12; }
            .notification-info { border-right: 4px solid #3498db; }

            .notification-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .notification-content i {
                font-size: 1.2rem;
            }

            .notification-success i { color: #27ae60; }
            .notification-error i { color: #e74c3c; }
            .notification-warning i { color: #f39c12; }
            .notification-info i { color: #3498db; }

            .notification-close {
                background: none;
                border: none;
                cursor: pointer;
                color: #7f8c8d;
                padding: 5px;
            }

            .notification-close:hover {
                color: #2c3e50;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(-100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @media (max-width: 480px) {
                #notification-container {
                    left: 10px;
                    right: 10px;
                }

                .notification {
                    min-width: auto;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    // ==================== FORM HELPERS ====================
    
    populateForm(formId, data) {
        const form = document.getElementById(formId);
        if (!form) return;

        Object.keys(data).forEach(key => {
            const input = form.querySelector(`[name="${key}"], #${key}`);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = data[key];
                } else if (input.type === 'file') {
                    // Handle file inputs separately
                } else {
                    input.value = data[key] || '';
                }
            }
        });
    }

    getFormData(formId) {
        const form = document.getElementById(formId);
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // Handle checkboxes
        form.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            data[checkbox.name || checkbox.id] = checkbox.checked;
        });

        return data;
    }

    validateForm(formId, rules = {}) {
        const form = document.getElementById(formId);
        if (!form) return { isValid: false, errors: ['Form not found'] };

        const errors = [];
        const data = this.getFormData(formId);

        Object.keys(rules).forEach(field => {
            const rule = rules[field];
            const value = data[field];

            if (rule.required && (!value || value.toString().trim() === '')) {
                errors.push(`${rule.label || field} مطلوب`);
            }

            if (value && rule.minLength && value.length < rule.minLength) {
                errors.push(`${rule.label || field} يجب أن يكون ${rule.minLength} أحرف على الأقل`);
            }

            if (value && rule.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                errors.push(`${rule.label || field} يجب أن يكون بريد إلكتروني صحيح`);
            }

            if (value && rule.phone && !/^[0-9+\-\s()]+$/.test(value)) {
                errors.push(`${rule.label || field} يجب أن يكون رقم هاتف صحيح`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors: errors,
            data: data
        };
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CRUDManager;
}
