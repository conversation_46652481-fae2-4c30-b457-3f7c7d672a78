<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }

        .stat-card .customers .icon { background: linear-gradient(135deg, #667eea, #764ba2); }
        .stat-card .suppliers .icon { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .stat-card .products .icon { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .stat-card .sales .icon { background: linear-gradient(135deg, #43e97b, #38f9d7); }

        .stat-card h3 {
            color: #2c3e50;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-card .change {
            font-size: 0.9rem;
            color: #27ae60;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .quick-actions {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .quick-actions h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .action-btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .action-btn i {
            margin-left: 10px;
        }

        .recent-activities {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .recent-activities h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
            <div class="user-info">
                <div class="user-avatar">A</div>
                <div>
                    <div style="font-weight: 600; color: #2c3e50;">مرحباً، admin</div>
                    <div style="font-size: 0.9rem; color: #7f8c8d;">مدير النظام</div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card customers" onclick="navigateTo('customers.html')">
                <div class="icon"><i class="fas fa-users"></i></div>
                <h3>إجمالي العملاء</h3>
                <div class="number" id="customersCount">0</div>
                <div class="change">+12% من الشهر الماضي</div>
            </div>
            
            <div class="stat-card suppliers" onclick="navigateTo('suppliers.html')">
                <div class="icon"><i class="fas fa-truck"></i></div>
                <h3>إجمالي الموردين</h3>
                <div class="number" id="suppliersCount">0</div>
                <div class="change">+8% من الشهر الماضي</div>
            </div>
            
            <div class="stat-card products" onclick="navigateTo('products.html')">
                <div class="icon"><i class="fas fa-box"></i></div>
                <h3>إجمالي المنتجات</h3>
                <div class="number" id="productsCount">0</div>
                <div class="change">+15% من الشهر الماضي</div>
            </div>
            
            <div class="stat-card sales">
                <div class="icon"><i class="fas fa-chart-line"></i></div>
                <h3>مبيعات اليوم</h3>
                <div class="number">25,430</div>
                <div class="change">+5% من أمس</div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="main-grid">
            <!-- Chart Section -->
            <div class="chart-section">
                <h3><i class="fas fa-chart-bar"></i> تحليل المبيعات الشهرية</h3>
                <div class="chart-placeholder">
                    <i class="fas fa-chart-area" style="font-size: 3rem; margin-left: 15px;"></i>
                    سيتم إضافة الرسوم البيانية قريباً
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
                <a href="customers.html" class="action-btn">
                    <i class="fas fa-user-plus"></i> إضافة عميل جديد
                </a>
                <a href="suppliers.html" class="action-btn">
                    <i class="fas fa-truck"></i> إضافة مورد جديد
                </a>
                <a href="products.html" class="action-btn">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </a>
                <a href="sales.html" class="action-btn">
                    <i class="fas fa-shopping-cart"></i> إنشاء فاتورة مبيعات
                </a>
                <a href="purchases.html" class="action-btn">
                    <i class="fas fa-file-invoice"></i> إنشاء فاتورة مشتريات
                </a>
                <a href="reports.html" class="action-btn">
                    <i class="fas fa-chart-bar"></i> عرض التقارير
                </a>
                <a href="settings.html" class="action-btn">
                    <i class="fas fa-cog"></i> إعدادات النظام
                </a>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="recent-activities">
            <h3><i class="fas fa-clock"></i> الأنشطة الأخيرة</h3>
            <div class="activity-item">
                <div class="activity-icon"><i class="fas fa-user-plus"></i></div>
                <div class="activity-content">
                    <div class="activity-title">تم إضافة عميل جديد: أحمد محمد</div>
                    <div class="activity-time">منذ 5 دقائق</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon"><i class="fas fa-box"></i></div>
                <div class="activity-content">
                    <div class="activity-title">تم إضافة منتج جديد: لابتوب HP</div>
                    <div class="activity-time">منذ 15 دقيقة</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon"><i class="fas fa-shopping-cart"></i></div>
                <div class="activity-content">
                    <div class="activity-title">تم إنشاء فاتورة مبيعات جديدة #1001</div>
                    <div class="activity-time">منذ 30 دقيقة</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحميل الإحصائيات من الخادم
        async function loadStats() {
            try {
                const response = await fetch('/api/system/stats');
                const data = await response.json();
                
                if (data.success) {
                    // تحديث الإحصائيات
                    document.getElementById('customersCount').textContent = data.data.database.customers || 0;
                    document.getElementById('suppliersCount').textContent = data.data.database.suppliers || 0;
                    document.getElementById('productsCount').textContent = data.data.database.products || 0;
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // التنقل إلى صفحة
        function navigateTo(page) {
            window.location.href = page;
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('authToken');
                window.location.href = 'index.html';
            }
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });
    </script>
</body>
</html>
