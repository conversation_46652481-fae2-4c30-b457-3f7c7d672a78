<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="components/header.js"></script>
    <script src="components/crud.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .search-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .products-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #2c3e50;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #ecf0f1;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .status-inactive {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .stock-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .stock-high {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .stock-medium {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
        }

        .stock-low {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .modal-header h3 {
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row.full {
            grid-template-columns: 1fr;
        }

        .image-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .image-upload:hover {
            border-color: #667eea;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin: 10px auto;
            display: block;
        }

        @media (max-width: 768px) {
            .search-grid {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 15px;
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }

            .table {
                font-size: 0.9rem;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-box"></i> إدارة المنتجات</h1>
            <div class="header-actions">
                <a href="dashboard.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                </a>
                <button class="btn btn-primary" onclick="openAddModal()">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-grid">
                <div class="form-group">
                    <label>البحث بالاسم</label>
                    <input type="text" class="form-control" id="searchName" placeholder="ادخل اسم المنتج">
                </div>
                <div class="form-group">
                    <label>الفئة</label>
                    <select class="form-control" id="searchCategory">
                        <option value="">جميع الفئات</option>
                        <option value="electronics">إلكترونيات</option>
                        <option value="computers">أجهزة كمبيوتر</option>
                        <option value="smartphones">هواتف ذكية</option>
                        <option value="appliances">أجهزة منزلية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>العلامة التجارية</label>
                    <select class="form-control" id="searchBrand">
                        <option value="">جميع العلامات</option>
                        <option value="samsung">سامسونج</option>
                        <option value="apple">آبل</option>
                        <option value="hp">إتش بي</option>
                        <option value="dell">ديل</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>الحالة</label>
                    <select class="form-control" id="searchStatus">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="searchProducts()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="products-table">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...
            </div>

            <div id="productsContent" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>الرقم</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>العلامة التجارية</th>
                            <th>السعر</th>
                            <th>المخزون</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>

                <div class="pagination" id="pagination">
                    <!-- أزرار التصفح -->
                </div>
            </div>

            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-box"></i>
                <h3>لا توجد منتجات</h3>
                <p>لم يتم العثور على أي منتجات. ابدأ بإضافة منتج جديد.</p>
            </div>
        </div>
    </div>

    <!-- Add/Edit Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة منتج جديد</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>

            <form id="productForm">
                <input type="hidden" id="productId">

                <div class="form-row">
                    <div class="form-group">
                        <label>اسم المنتج *</label>
                        <input type="text" class="form-control" id="productName" required>
                    </div>
                    <div class="form-group">
                        <label>رمز المنتج *</label>
                        <input type="text" class="form-control" id="productCode" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>الفئة *</label>
                        <select class="form-control" id="category" required>
                            <option value="">اختر الفئة</option>
                            <option value="electronics">إلكترونيات</option>
                            <option value="computers">أجهزة كمبيوتر</option>
                            <option value="smartphones">هواتف ذكية</option>
                            <option value="appliances">أجهزة منزلية</option>
                            <option value="spare_parts">قطع غيار</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>العلامة التجارية</label>
                        <select class="form-control" id="brand">
                            <option value="">اختر العلامة التجارية</option>
                            <option value="samsung">سامسونج</option>
                            <option value="apple">آبل</option>
                            <option value="hp">إتش بي</option>
                            <option value="dell">ديل</option>
                            <option value="lg">إل جي</option>
                        </select>
                    </div>
                </div>

                <div class="form-row full">
                    <div class="form-group">
                        <label>وصف المنتج</label>
                        <textarea class="form-control" id="description" rows="3"></textarea>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>سعر الشراء *</label>
                        <input type="number" class="form-control" id="purchasePrice" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>سعر البيع *</label>
                        <input type="number" class="form-control" id="salePrice" step="0.01" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>الكمية الحالية</label>
                        <input type="number" class="form-control" id="currentStock" value="0">
                    </div>
                    <div class="form-group">
                        <label>الحد الأدنى للمخزون</label>
                        <input type="number" class="form-control" id="minStock" value="5">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>وحدة القياس</label>
                        <select class="form-control" id="unit">
                            <option value="piece">قطعة</option>
                            <option value="kg">كيلوجرام</option>
                            <option value="meter">متر</option>
                            <option value="liter">لتر</option>
                            <option value="box">صندوق</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الحالة</label>
                        <select class="form-control" id="status">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>

                <div class="form-row full">
                    <div class="form-group">
                        <label>صورة المنتج</label>
                        <div class="image-upload" onclick="document.getElementById('imageInput').click()">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #ddd; margin-bottom: 10px;"></i>
                            <p>انقر لرفع صورة المنتج</p>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="previewImage(this)">
                            <img id="imagePreview" class="image-preview" style="display: none;">
                        </div>
                    </div>
                </div>

                <div class="form-row full">
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                    </div>
                </div>

                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let products = [];
        let currentPage = 1;
        let totalPages = 1;
        const itemsPerPage = 10;

        // تحميل المنتجات
        async function loadProducts() {
            try {
                document.getElementById('loadingIndicator').style.display = 'block';
                document.getElementById('productsContent').style.display = 'none';
                document.getElementById('emptyState').style.display = 'none';

                const response = await fetch('/api/products');
                const data = await response.json();

                if (data.success) {
                    products = data.data || [];
                    displayProducts();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل المنتجات:', error);
                // عرض بيانات تجريبية
                products = [
                    {
                        id: 1,
                        productName: 'لابتوب HP Pavilion',
                        productCode: 'HP-PAV-001',
                        category: 'computers',
                        brand: 'hp',
                        description: 'لابتوب HP Pavilion بمعالج Intel Core i5',
                        purchasePrice: 2500,
                        salePrice: 3000,
                        currentStock: 15,
                        minStock: 5,
                        unit: 'piece',
                        status: 'active',
                        imageUrl: null,
                        createdAt: '2024-01-15'
                    },
                    {
                        id: 2,
                        productName: 'هاتف Samsung Galaxy S24',
                        productCode: 'SAM-S24-001',
                        category: 'smartphones',
                        brand: 'samsung',
                        description: 'هاتف Samsung Galaxy S24 بذاكرة 256GB',
                        purchasePrice: 3500,
                        salePrice: 4200,
                        currentStock: 8,
                        minStock: 10,
                        unit: 'piece',
                        status: 'active',
                        imageUrl: null,
                        createdAt: '2024-01-20'
                    }
                ];
                displayProducts();
            }
        }

        // عرض المنتجات
        function displayProducts() {
            const tbody = document.getElementById('productsTableBody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProducts = products.slice(startIndex, endIndex);

            if (products.length === 0) {
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('emptyState').style.display = 'block';
                return;
            }

            tbody.innerHTML = '';
            pageProducts.forEach(product => {
                const categoryText = {
                    'electronics': 'إلكترونيات',
                    'computers': 'أجهزة كمبيوتر',
                    'smartphones': 'هواتف ذكية',
                    'appliances': 'أجهزة منزلية',
                    'spare_parts': 'قطع غيار'
                };

                const brandText = {
                    'samsung': 'سامسونج',
                    'apple': 'آبل',
                    'hp': 'إتش بي',
                    'dell': 'ديل',
                    'lg': 'إل جي'
                };

                // تحديد حالة المخزون
                let stockClass = 'stock-high';
                if (product.currentStock <= product.minStock) {
                    stockClass = 'stock-low';
                } else if (product.currentStock <= product.minStock * 2) {
                    stockClass = 'stock-medium';
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <img src="${product.imageUrl || '/images/no-image.png'}"
                             alt="${product.productName}"
                             class="product-image"
                             onerror="this.src='/images/no-image.png'">
                    </td>
                    <td>${product.id}</td>
                    <td>
                        <div style="font-weight: 600;">${product.productName}</div>
                        <div style="font-size: 0.85rem; color: #7f8c8d;">${product.productCode}</div>
                    </td>
                    <td>${categoryText[product.category] || product.category}</td>
                    <td>${brandText[product.brand] || product.brand || '-'}</td>
                    <td>
                        <div style="font-weight: 600; color: #27ae60;">${product.salePrice.toLocaleString()} ر.س</div>
                        <div style="font-size: 0.85rem; color: #7f8c8d;">التكلفة: ${product.purchasePrice.toLocaleString()} ر.س</div>
                    </td>
                    <td>
                        <span class="stock-badge ${stockClass}">
                            ${product.currentStock} ${product.unit || 'قطعة'}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${product.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${product.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-info btn-sm" onclick="viewProduct(${product.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="editProduct(${product.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('productsContent').style.display = 'block';

            updatePagination();
        }

        // تحديث التصفح
        function updatePagination() {
            totalPages = Math.ceil(products.length / itemsPerPage);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                const button = document.createElement('button');
                button.textContent = i;
                button.className = i === currentPage ? 'active' : '';
                button.onclick = () => {
                    currentPage = i;
                    displayProducts();
                };
                pagination.appendChild(button);
            }
        }

        // فتح نافذة الإضافة
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة منتج جديد';
            document.getElementById('productForm').reset();
            document.getElementById('productId').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('productModal').style.display = 'block';
        }

        // إغلاق النافذة
        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
        }

        // معاينة الصورة
        function previewImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // حفظ المنتج
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('productName', document.getElementById('productName').value);
            formData.append('productCode', document.getElementById('productCode').value);
            formData.append('category', document.getElementById('category').value);
            formData.append('brand', document.getElementById('brand').value);
            formData.append('description', document.getElementById('description').value);
            formData.append('purchasePrice', document.getElementById('purchasePrice').value);
            formData.append('salePrice', document.getElementById('salePrice').value);
            formData.append('currentStock', document.getElementById('currentStock').value);
            formData.append('minStock', document.getElementById('minStock').value);
            formData.append('unit', document.getElementById('unit').value);
            formData.append('status', document.getElementById('status').value);
            formData.append('notes', document.getElementById('notes').value);

            const imageInput = document.getElementById('imageInput');
            if (imageInput.files[0]) {
                formData.append('image', imageInput.files[0]);
            }

            try {
                const productId = document.getElementById('productId').value;
                const url = productId ? `/api/products/${productId}` : '/api/products';
                const method = productId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    alert(productId ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح');
                    closeModal();
                    loadProducts();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            } catch (error) {
                console.error('خطأ في حفظ المنتج:', error);
                alert('حدث خطأ في حفظ البيانات');
            }
        });

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
        });

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('productModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>