{"name": "commercial-agency-backend", "version": "1.0.0", "description": "نظام الوكالة التجارية المتكامل - الخادم الخلفي", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node src/scripts/migrate.js", "db:seed": "node src/scripts/seed.js", "db:reset": "node src/scripts/reset.js", "build": "babel src -d dist", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["commercial", "agency", "erp", "inventory", "sales", "purchase", "nodejs", "express", "mysql"], "author": "Commercial Agency System", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "lodash": "^4.17.21", "uuid": "^9.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "express-async-errors": "^3.1.1", "http-status-codes": "^2.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "pdf-lib": "^1.17.1", "pdfkit": "^0.13.0", "exceljs": "^4.4.0", "qrcode": "^1.5.3", "node-cron": "^3.0.3", "socket.io": "^4.7.4", "redis": "^4.6.10", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-jwt": "^4.0.1", "express-fileupload": "^1.4.3", "archiver": "^6.0.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/cli": "^7.23.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/commercial-agency/backend.git"}, "bugs": {"url": "https://github.com/commercial-agency/backend/issues"}, "homepage": "https://github.com/commercial-agency/backend#readme", "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/*.js"]}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}