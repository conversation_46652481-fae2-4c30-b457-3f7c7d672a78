#!/usr/bin/env node

/**
 * برنامج نصي لإنشاء البيانات الأساسية
 * Database Seeding Script
 */

const dotenv = require('dotenv');
const path = require('path');

// تحميل متغيرات البيئة
dotenv.config({ path: path.join(__dirname, '../../.env') });

const { seedDatabase } = require('../models');
const { initializeDatabase } = require('../config/database');

// خيارات سطر الأوامر
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force') || args.includes('-f'),
  demo: args.includes('--demo') || args.includes('-d'),
  minimal: args.includes('--minimal') || args.includes('-m'),
  help: args.includes('--help') || args.includes('-h')
};

// عرض المساعدة
const showHelp = () => {
  console.log(`
🌱 برنامج إنشاء البيانات الأساسية - Commercial Agency System

الاستخدام:
  node seed.js [خيارات]

الخيارات:
  -f, --force      إعادة إنشاء البيانات حتى لو كانت موجودة
  -d, --demo       إنشاء بيانات تجريبية للعرض
  -m, --minimal    إنشاء الحد الأدنى من البيانات الأساسية فقط
  -h, --help       عرض هذه المساعدة

أمثلة:
  node seed.js                    # إنشاء البيانات الأساسية
  node seed.js --demo             # إنشاء بيانات تجريبية
  node seed.js --minimal          # الحد الأدنى من البيانات
  node seed.js --force            # إعادة إنشاء البيانات
  `);
};

// دالة إنشاء البيانات التجريبية
const createDemoData = async () => {
  try {
    console.log('🎭 إنشاء البيانات التجريبية...');
    
    const { User, Role, Customer, Supplier, Product } = require('../models');
    
    // إنشاء مستخدمين تجريبيين
    console.log('👥 إنشاء مستخدمين تجريبيين...');
    
    const salesManagerRole = await Role.findByName('sales_manager');
    const purchaseManagerRole = await Role.findByName('purchase_manager');
    const accountantRole = await Role.findByName('accountant');
    
    const demoUsers = [
      {
        username: 'sales_manager',
        email: '<EMAIL>',
        password_hash: 'Sales@123456',
        first_name: 'أحمد',
        last_name: 'المبيعات',
        phone: '+************',
        role_id: salesManagerRole?.id,
        is_active: true,
        email_verified: true
      },
      {
        username: 'purchase_manager',
        email: '<EMAIL>',
        password_hash: 'Purchase@123456',
        first_name: 'محمد',
        last_name: 'المشتريات',
        phone: '+************',
        role_id: purchaseManagerRole?.id,
        is_active: true,
        email_verified: true
      },
      {
        username: 'accountant',
        email: '<EMAIL>',
        password_hash: 'Account@123456',
        first_name: 'فاطمة',
        last_name: 'المحاسبة',
        phone: '+************',
        role_id: accountantRole?.id,
        is_active: true,
        email_verified: true
      }
    ];
    
    for (const userData of demoUsers) {
      const existingUser = await User.findByUsername(userData.username);
      if (!existingUser || options.force) {
        if (existingUser && options.force) {
          await existingUser.destroy();
        }
        await User.createUser(userData);
        console.log(`✅ تم إنشاء المستخدم: ${userData.username}`);
      }
    }
    
    // إنشاء عملاء تجريبيين
    console.log('🏢 إنشاء عملاء تجريبيين...');
    
    const demoCustomers = [
      {
        customer_type: 'company',
        company_name: 'شركة التقنية المتقدمة',
        email: '<EMAIL>',
        phone: '+************',
        mobile: '+************',
        tax_number: '***************',
        commercial_register: '**********',
        address: 'طريق الملك فهد، الرياض',
        city: 'الرياض',
        customer_category: 'wholesale',
        credit_limit: 100000,
        payment_terms: 30,
        discount_percentage: 5
      },
      {
        customer_type: 'individual',
        first_name: 'عبدالله',
        last_name: 'الأحمد',
        email: '<EMAIL>',
        phone: '+966114567891',
        mobile: '+966502222222',
        national_id: '1234567890',
        address: 'حي النخيل، الرياض',
        city: 'الرياض',
        customer_category: 'retail',
        credit_limit: 10000,
        payment_terms: 15
      },
      {
        customer_type: 'company',
        company_name: 'مؤسسة التجارة الذكية',
        email: '<EMAIL>',
        phone: '+966114567892',
        mobile: '+966503333333',
        tax_number: '300123456789004',
        commercial_register: '1010222222',
        address: 'شارع العليا، الرياض',
        city: 'الرياض',
        customer_category: 'vip',
        credit_limit: 200000,
        payment_terms: 45,
        discount_percentage: 10
      }
    ];
    
    for (const customerData of demoCustomers) {
      const existingCustomer = await Customer.findOne({
        where: { 
          [customerData.customer_type === 'company' ? 'company_name' : 'first_name']: 
          customerData.customer_type === 'company' ? customerData.company_name : customerData.first_name
        }
      });
      
      if (!existingCustomer || options.force) {
        if (existingCustomer && options.force) {
          await existingCustomer.destroy();
        }
        await Customer.createCustomer(customerData);
        const displayName = customerData.customer_type === 'company' ? 
          customerData.company_name : `${customerData.first_name} ${customerData.last_name}`;
        console.log(`✅ تم إنشاء العميل: ${displayName}`);
      }
    }
    
    // إنشاء موردين تجريبيين
    console.log('🏭 إنشاء موردين تجريبيين...');
    
    const demoSuppliers = [
      {
        company_name: 'شركة الإلكترونيات المتطورة',
        contact_person: 'خالد المهندس',
        email: '<EMAIL>',
        phone: '+966114567893',
        mobile: '+966504444444',
        tax_number: '300123456789005',
        commercial_register: '1010333333',
        address: 'المنطقة الصناعية الثانية، الرياض',
        city: 'الرياض',
        supplier_type: 'local',
        credit_limit: 500000,
        payment_terms: 60,
        rating: 4.5
      },
      {
        company_name: 'Global Tech Supplies',
        contact_person: 'John Smith',
        email: '<EMAIL>',
        phone: '+1234567890',
        website: 'https://globaltech.com',
        address: '123 Tech Street, Silicon Valley',
        city: 'San Francisco',
        country: 'United States',
        supplier_type: 'international',
        credit_limit: 1000000,
        payment_terms: 90,
        rating: 4.8
      },
      {
        company_name: 'مصنع الأجهزة الذكية',
        contact_person: 'سارة المطور',
        email: '<EMAIL>',
        phone: '+966114567894',
        mobile: '+966505555555',
        tax_number: '300123456789006',
        commercial_register: '1010444444',
        address: 'مدينة الملك عبدالعزيز للعلوم والتقنية',
        city: 'الرياض',
        supplier_type: 'local',
        credit_limit: 300000,
        payment_terms: 45,
        rating: 4.2
      }
    ];
    
    for (const supplierData of demoSuppliers) {
      const existingSupplier = await Supplier.findOne({
        where: { company_name: supplierData.company_name }
      });
      
      if (!existingSupplier || options.force) {
        if (existingSupplier && options.force) {
          await existingSupplier.destroy();
        }
        await Supplier.createSupplier(supplierData);
        console.log(`✅ تم إنشاء المورد: ${supplierData.company_name}`);
      }
    }
    
    console.log('🎉 تم إنشاء البيانات التجريبية بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error.message);
    throw error;
  }
};

// دالة إنشاء البيانات الأساسية المحدودة
const createMinimalData = async () => {
  try {
    console.log('📦 إنشاء الحد الأدنى من البيانات الأساسية...');
    
    // إنشاء الأدوار والمستخدم الافتراضي فقط
    await seedDatabase();
    
    console.log('✅ تم إنشاء الحد الأدنى من البيانات الأساسية');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الأساسية المحدودة:', error.message);
    throw error;
  }
};

// دالة الإنشاء الرئيسية
const runSeeding = async () => {
  try {
    console.log('🚀 بدء عملية إنشاء البيانات...');
    console.log(`📅 التاريخ والوقت: ${new Date().toLocaleString('ar-SA')}`);
    console.log(`🌍 البيئة: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🗄️  قاعدة البيانات: ${process.env.DB_NAME}`);
    
    // تهيئة قاعدة البيانات
    console.log('🔌 الاتصال بقاعدة البيانات...');
    await initializeDatabase();
    
    if (options.minimal) {
      await createMinimalData();
    } else {
      // إنشاء البيانات الأساسية
      await seedDatabase();
      
      if (options.demo) {
        await createDemoData();
      }
    }
    
    console.log('🎉 انتهت عملية إنشاء البيانات بنجاح');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات:', error.message);
    
    if (process.env.NODE_ENV === 'development') {
      console.error('📋 تفاصيل الخطأ:', error.stack);
    }
    
    process.exit(1);
  }
};

// تنفيذ البرنامج النصي
const main = async () => {
  if (options.help) {
    showHelp();
    process.exit(0);
  }
  
  await runSeeding();
};

// تشغيل البرنامج النصي
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ فشل في تنفيذ إنشاء البيانات:', error.message);
    process.exit(1);
  });
}

module.exports = {
  runSeeding,
  createDemoData,
  createMinimalData
};
