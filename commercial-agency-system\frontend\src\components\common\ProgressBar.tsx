import React from 'react';
import { useRouter } from 'next/router';
import NProgress from 'nprogress';
import { styled } from '@mui/material/styles';

// تصميم شريط التقدم
const ProgressBarStyles = styled('div')(({ theme }) => ({
  '& #nprogress': {
    pointerEvents: 'none',
  },
  '& #nprogress .bar': {
    background: theme.palette.primary.main,
    position: 'fixed',
    zIndex: theme.zIndex.tooltip + 1,
    top: 0,
    left: 0,
    width: '100%',
    height: '3px',
  },
  '& #nprogress .peg': {
    display: 'block',
    position: 'absolute',
    right: '0px',
    width: '100px',
    height: '100%',
    boxShadow: `0 0 10px ${theme.palette.primary.main}, 0 0 5px ${theme.palette.primary.main}`,
    opacity: 1,
    transform: 'rotate(3deg) translate(0px, -4px)',
  },
  '& #nprogress .spinner': {
    display: 'block',
    position: 'fixed',
    zIndex: theme.zIndex.tooltip + 1,
    top: '15px',
    right: '15px',
  },
  '& #nprogress .spinner-icon': {
    width: '18px',
    height: '18px',
    boxSizing: 'border-box',
    border: `solid 2px transparent`,
    borderTopColor: theme.palette.primary.main,
    borderLeftColor: theme.palette.primary.main,
    borderRadius: '50%',
    animation: 'nprogress-spinner 400ms linear infinite',
  },
  '& .nprogress-custom-parent': {
    overflow: 'hidden',
    position: 'relative',
  },
  '& .nprogress-custom-parent #nprogress .spinner, & .nprogress-custom-parent #nprogress .bar': {
    position: 'absolute',
  },
  '@keyframes nprogress-spinner': {
    '0%': { transform: 'rotate(0deg)' },
    '100%': { transform: 'rotate(360deg)' },
  },
}));

// إعدادات NProgress
NProgress.configure({
  minimum: 0.3,
  easing: 'ease',
  speed: 500,
  showSpinner: true,
  trickleSpeed: 200,
  parent: 'body',
});

const ProgressBar: React.FC = () => {
  const router = useRouter();

  React.useEffect(() => {
    let timeout: NodeJS.Timeout;

    const handleStart = (url: string) => {
      // تجنب عرض شريط التقدم للتنقل السريع
      timeout = setTimeout(() => {
        NProgress.start();
      }, 100);
    };

    const handleComplete = () => {
      clearTimeout(timeout);
      NProgress.done();
    };

    const handleError = () => {
      clearTimeout(timeout);
      NProgress.done();
    };

    // الاستماع لأحداث التوجيه
    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleError);

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleError);
      clearTimeout(timeout);
    };
  }, [router]);

  return <ProgressBarStyles />;
};

// Hook لاستخدام شريط التقدم يدوياً
export const useProgress = () => {
  const start = React.useCallback(() => {
    NProgress.start();
  }, []);

  const done = React.useCallback(() => {
    NProgress.done();
  }, []);

  const set = React.useCallback((progress: number) => {
    NProgress.set(progress);
  }, []);

  const inc = React.useCallback((amount?: number) => {
    NProgress.inc(amount);
  }, []);

  return { start, done, set, inc };
};

// مكون شريط تقدم مخصص للعمليات الطويلة
export const CustomProgressBar: React.FC<{
  progress: number;
  message?: string;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}> = ({ progress, message, color = 'primary' }) => {
  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        padding: '10px 20px',
        borderBottom: '1px solid #e0e0e0',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '8px',
        }}
      >
        {message && (
          <span style={{ fontSize: '14px', color: '#666' }}>
            {message}
          </span>
        )}
        <span style={{ fontSize: '14px', fontWeight: 'bold' }}>
          {Math.round(progress)}%
        </span>
      </div>
      <div
        style={{
          width: '100%',
          height: '4px',
          backgroundColor: '#f0f0f0',
          borderRadius: '2px',
          overflow: 'hidden',
        }}
      >
        <div
          style={{
            width: `${progress}%`,
            height: '100%',
            backgroundColor: color === 'primary' ? '#2196f3' : 
                           color === 'success' ? '#4caf50' :
                           color === 'warning' ? '#ff9800' :
                           color === 'error' ? '#f44336' : '#9c27b0',
            transition: 'width 0.3s ease',
            borderRadius: '2px',
          }}
        />
      </div>
    </div>
  );
};

// Hook للتحكم في شريط التقدم المخصص
export const useCustomProgress = () => {
  const [progress, setProgress] = React.useState(0);
  const [message, setMessage] = React.useState<string | undefined>();
  const [visible, setVisible] = React.useState(false);

  const start = React.useCallback((initialMessage?: string) => {
    setProgress(0);
    setMessage(initialMessage);
    setVisible(true);
  }, []);

  const update = React.useCallback((newProgress: number, newMessage?: string) => {
    setProgress(Math.min(100, Math.max(0, newProgress)));
    if (newMessage !== undefined) {
      setMessage(newMessage);
    }
  }, []);

  const complete = React.useCallback(() => {
    setProgress(100);
    setTimeout(() => {
      setVisible(false);
      setProgress(0);
      setMessage(undefined);
    }, 500);
  }, []);

  const hide = React.useCallback(() => {
    setVisible(false);
    setProgress(0);
    setMessage(undefined);
  }, []);

  return {
    progress,
    message,
    visible,
    start,
    update,
    complete,
    hide,
  };
};

export default ProgressBar;
