<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والتحليلات - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .report-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .report-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 20px;
        }

        .report-card .sales .icon { background: linear-gradient(135deg, #43e97b, #38f9d7); }
        .report-card .purchases .icon { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .report-card .inventory .icon { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .report-card .customers .icon { background: linear-gradient(135deg, #667eea, #764ba2); }
        .report-card .suppliers .icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .report-card .financial .icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }

        .report-card h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            margin-bottom: 10px;
        }

        .report-card p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .report-card .actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .filters-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .chart-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .chart-placeholder {
            height: 400px;
            background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 1.1rem;
            flex-direction: column;
            gap: 15px;
        }

        .chart-placeholder i {
            font-size: 3rem;
            opacity: 0.5;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .summary-card .value {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-card .label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .summary-card .change {
            font-size: 0.85rem;
            margin-top: 5px;
        }

        .change.positive {
            color: #27ae60;
        }

        .change.negative {
            color: #e74c3c;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-chart-bar"></i> التقارير والتحليلات</h1>
            <div class="header-actions">
                <a href="dashboard.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                </a>
                <button class="btn btn-primary" onclick="exportReport()">
                    <i class="fas fa-download"></i> تصدير التقرير
                </button>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <h3 style="color: #2c3e50; margin-bottom: 20px;"><i class="fas fa-filter"></i> فلاتر التقارير</h3>
            <div class="filters-grid">
                <div class="form-group">
                    <label>نوع التقرير</label>
                    <select class="form-control" id="reportType">
                        <option value="sales">تقرير المبيعات</option>
                        <option value="purchases">تقرير المشتريات</option>
                        <option value="inventory">تقرير المخزون</option>
                        <option value="customers">تقرير العملاء</option>
                        <option value="suppliers">تقرير الموردين</option>
                        <option value="financial">التقرير المالي</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>من تاريخ</label>
                    <input type="date" class="form-control" id="fromDate">
                </div>
                <div class="form-group">
                    <label>إلى تاريخ</label>
                    <input type="date" class="form-control" id="toDate">
                </div>
                <div class="form-group">
                    <label>الفترة</label>
                    <select class="form-control" id="period">
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-chart-line"></i> إنشاء التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="value" id="totalRevenue">0 ر.س</div>
                <div class="label">إجمالي الإيرادات</div>
                <div class="change positive" id="revenueChange">+0%</div>
            </div>
            <div class="summary-card">
                <div class="value" id="totalExpenses">0 ر.س</div>
                <div class="label">إجمالي المصروفات</div>
                <div class="change negative" id="expensesChange">+0%</div>
            </div>
            <div class="summary-card">
                <div class="value" id="netProfit">0 ر.س</div>
                <div class="label">صافي الربح</div>
                <div class="change positive" id="profitChange">+0%</div>
            </div>
            <div class="summary-card">
                <div class="value" id="totalTransactions">0</div>
                <div class="label">إجمالي المعاملات</div>
                <div class="change positive" id="transactionsChange">+0%</div>
            </div>
        </div>

        <!-- Chart Section -->
        <div class="chart-section">
            <h3><i class="fas fa-chart-area"></i> الرسم البياني للمبيعات والمشتريات</h3>
            <div class="chart-placeholder">
                <i class="fas fa-chart-line"></i>
                <p>سيتم عرض الرسم البياني هنا</p>
                <small>يمكن إضافة مكتبة Chart.js أو أي مكتبة رسوم بيانية أخرى</small>
            </div>
        </div>

        <!-- Reports Grid -->
        <div class="reports-grid">
            <div class="report-card sales" onclick="generateSpecificReport('sales')">
                <div class="icon"><i class="fas fa-shopping-cart"></i></div>
                <h3>تقرير المبيعات</h3>
                <p>تقرير شامل عن جميع عمليات البيع والإيرادات خلال فترة محددة</p>
                <div class="actions">
                    <button class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="report-card purchases" onclick="generateSpecificReport('purchases')">
                <div class="icon"><i class="fas fa-file-invoice"></i></div>
                <h3>تقرير المشتريات</h3>
                <p>تقرير مفصل عن جميع عمليات الشراء والمصروفات من الموردين</p>
                <div class="actions">
                    <button class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="report-card inventory" onclick="generateSpecificReport('inventory')">
                <div class="icon"><i class="fas fa-boxes"></i></div>
                <h3>تقرير المخزون</h3>
                <p>تقرير عن حالة المخزون الحالية والحركات والمنتجات الناقصة</p>
                <div class="actions">
                    <button class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="report-card customers" onclick="generateSpecificReport('customers')">
                <div class="icon"><i class="fas fa-users"></i></div>
                <h3>تقرير العملاء</h3>
                <p>تقرير عن أداء العملاء ومبيعاتهم والمديونيات المستحقة</p>
                <div class="actions">
                    <button class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="report-card suppliers" onclick="generateSpecificReport('suppliers')">
                <div class="icon"><i class="fas fa-truck"></i></div>
                <h3>تقرير الموردين</h3>
                <p>تقرير عن أداء الموردين ومشترياتهم والمديونيات المستحقة</p>
                <div class="actions">
                    <button class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="report-card financial" onclick="generateSpecificReport('financial')">
                <div class="icon"><i class="fas fa-chart-pie"></i></div>
                <h3>التقرير المالي</h3>
                <p>تقرير شامل عن الوضع المالي والأرباح والخسائر</p>
                <div class="actions">
                    <button class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحميل البيانات الأولية
        document.addEventListener('DOMContentLoaded', function() {
            loadSummaryData();
            setDefaultDates();
        });

        // تعيين التواريخ الافتراضية
        function setDefaultDates() {
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('fromDate').value = firstDayOfMonth.toISOString().split('T')[0];
            document.getElementById('toDate').value = today.toISOString().split('T')[0];
        }

        // تحميل بيانات الملخص
        async function loadSummaryData() {
            try {
                // محاكاة تحميل البيانات
                const summaryData = {
                    totalRevenue: 125000,
                    totalExpenses: 85000,
                    netProfit: 40000,
                    totalTransactions: 156,
                    revenueChange: 12.5,
                    expensesChange: 8.3,
                    profitChange: 18.7,
                    transactionsChange: 15.2
                };

                document.getElementById('totalRevenue').textContent = summaryData.totalRevenue.toLocaleString() + ' ر.س';
                document.getElementById('totalExpenses').textContent = summaryData.totalExpenses.toLocaleString() + ' ر.س';
                document.getElementById('netProfit').textContent = summaryData.netProfit.toLocaleString() + ' ر.س';
                document.getElementById('totalTransactions').textContent = summaryData.totalTransactions.toLocaleString();

                document.getElementById('revenueChange').textContent = '+' + summaryData.revenueChange + '%';
                document.getElementById('expensesChange').textContent = '+' + summaryData.expensesChange + '%';
                document.getElementById('profitChange').textContent = '+' + summaryData.profitChange + '%';
                document.getElementById('transactionsChange').textContent = '+' + summaryData.transactionsChange + '%';

            } catch (error) {
                console.error('خطأ في تحميل بيانات الملخص:', error);
            }
        }

        // إنشاء تقرير عام
        function generateReport() {
            const reportType = document.getElementById('reportType').value;
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;
            const period = document.getElementById('period').value;

            console.log('إنشاء تقرير:', {
                type: reportType,
                from: fromDate,
                to: toDate,
                period: period
            });

            alert('جاري إنشاء التقرير...');
        }

        // إنشاء تقرير محدد
        function generateSpecificReport(type) {
            console.log('إنشاء تقرير محدد:', type);
            alert(`جاري إنشاء تقرير ${getReportTypeName(type)}...`);
        }

        // الحصول على اسم نوع التقرير
        function getReportTypeName(type) {
            const names = {
                'sales': 'المبيعات',
                'purchases': 'المشتريات',
                'inventory': 'المخزون',
                'customers': 'العملاء',
                'suppliers': 'الموردين',
                'financial': 'المالي'
            };
            return names[type] || type;
        }

        // تصدير التقرير
        function exportReport() {
            const reportType = document.getElementById('reportType').value;
            console.log('تصدير التقرير:', reportType);
            alert('جاري تصدير التقرير...');
        }

        // تغيير الفترة
        document.getElementById('period').addEventListener('change', function() {
            const period = this.value;
            const today = new Date();
            let fromDate, toDate;

            switch(period) {
                case 'today':
                    fromDate = toDate = today;
                    break;
                case 'week':
                    fromDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    toDate = today;
                    break;
                case 'month':
                    fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    toDate = today;
                    break;
                case 'quarter':
                    const quarter = Math.floor(today.getMonth() / 3);
                    fromDate = new Date(today.getFullYear(), quarter * 3, 1);
                    toDate = today;
                    break;
                case 'year':
                    fromDate = new Date(today.getFullYear(), 0, 1);
                    toDate = today;
                    break;
                default:
                    return;
            }

            if (period !== 'custom') {
                document.getElementById('fromDate').value = fromDate.toISOString().split('T')[0];
                document.getElementById('toDate').value = toDate.toISOString().split('T')[0];
            }
        });
    </script>
</body>
</html>
