<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مبيعات جديدة - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .invoice-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ecf0f1;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-grid.full {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .coming-soon {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .coming-soon i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
            color: #667eea;
        }

        .coming-soon h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .coming-soon p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .feature-list {
            text-align: right;
            max-width: 600px;
            margin: 0 auto;
        }

        .feature-list ul {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            color: #27ae60;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-file-invoice-dollar"></i> فاتورة مبيعات جديدة</h1>
            <a href="sales.html" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة لقائمة المبيعات
            </a>
        </div>

        <!-- Invoice Form -->
        <div class="invoice-form">
            <div class="coming-soon">
                <i class="fas fa-tools"></i>
                <h2>قيد التطوير</h2>
                <p>نحن نعمل بجد لإنجاز هذه الميزة. ستكون متاحة قريباً مع جميع الوظائف المطلوبة.</p>
                
                <div class="feature-list">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">الميزات القادمة:</h3>
                    <ul>
                        <li>
                            <i class="fas fa-check"></i>
                            إنشاء فواتير مبيعات تفاعلية
                        </li>
                        <li>
                            <i class="fas fa-check"></i>
                            اختيار العملاء من قاعدة البيانات
                        </li>
                        <li>
                            <i class="fas fa-check"></i>
                            إضافة المنتجات مع الكميات والأسعار
                        </li>
                        <li>
                            <i class="fas fa-check"></i>
                            حساب الضرائب والخصومات تلقائياً
                        </li>
                        <li>
                            <i class="fas fa-check"></i>
                            طباعة وتصدير الفواتير
                        </li>
                        <li>
                            <i class="fas fa-check"></i>
                            تتبع حالة الدفع
                        </li>
                        <li>
                            <i class="fas fa-check"></i>
                            إرسال الفواتير بالبريد الإلكتروني
                        </li>
                        <li>
                            <i class="fas fa-check"></i>
                            تحديث المخزون تلقائياً
                        </li>
                    </ul>
                </div>

                <div style="margin-top: 40px;">
                    <a href="dashboard.html" class="btn btn-secondary">
                        <i class="fas fa-home"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
