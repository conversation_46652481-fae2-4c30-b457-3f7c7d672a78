import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  selectSettings,
  selectThemeSettings,
  selectSystemSettings,
  updateThemeSettings,
  updateSystemSettings,
  toggleThemeMode,
  updateLanguage,
  updateCurrency,
} from '@/store/slices/settingsSlice';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import { arSA, enUS } from '@mui/material/locale';
import { CacheProvider } from '@emotion/react';
import createEmotionCache from '@/utils/createEmotionCache';
import rtlPlugin from 'stylis-plugin-rtl';
import { prefixer } from 'stylis';
import createCache from '@emotion/cache';

// تعريف أنواع البيانات
interface SettingsContextType {
  // إعدادات الثيم
  themeMode: 'light' | 'dark' | 'auto';
  primaryColor: string;
  secondaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
  
  // إعدادات النظام
  language: 'ar' | 'en';
  currency: string;
  timezone: string;
  dateFormat: string;
  
  // دوال التحكم
  toggleTheme: () => void;
  setThemeMode: (mode: 'light' | 'dark' | 'auto') => void;
  setPrimaryColor: (color: string) => void;
  setSecondaryColor: (color: string) => void;
  setFontSize: (size: 'small' | 'medium' | 'large') => void;
  setLanguage: (language: 'ar' | 'en') => void;
  setCurrency: (currency: string) => void;
  setTimezone: (timezone: string) => void;
  setDateFormat: (format: string) => void;
  
  // دوال مساعدة
  formatCurrency: (amount: number) => string;
  formatDate: (date: Date | string) => string;
  formatTime: (date: Date | string) => string;
  isRTL: boolean;
}

// إنشاء Context
const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

// Hook لاستخدام SettingsContext
export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

// خصائص SettingsProvider
interface SettingsProviderProps {
  children: ReactNode;
}

// مكون SettingsProvider
export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const settings = useAppSelector(selectSettings);
  const themeSettings = useAppSelector(selectThemeSettings);
  const systemSettings = useAppSelector(selectSystemSettings);

  // إنشاء Emotion cache للـ RTL
  const cacheRtl = createCache({
    key: 'muirtl',
    stylisPlugins: [prefixer, rtlPlugin],
  });

  const cacheLtr = createEmotionCache();

  // تحديد الثيم الحالي
  const getActualThemeMode = () => {
    if (themeSettings.mode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return themeSettings.mode;
  };

  // إنشاء ثيم Material-UI
  const createMuiTheme = () => {
    const mode = getActualThemeMode();
    const isRTL = systemSettings.defaultLanguage === 'ar';
    const locale = isRTL ? arSA : enUS;

    return createTheme({
      direction: isRTL ? 'rtl' : 'ltr',
      palette: {
        mode,
        primary: {
          main: themeSettings.primaryColor,
        },
        secondary: {
          main: themeSettings.secondaryColor,
        },
      },
      typography: {
        fontFamily: themeSettings.fontFamily,
        fontSize: themeSettings.fontSize === 'small' ? 12 : 
                  themeSettings.fontSize === 'large' ? 16 : 14,
      },
      components: {
        MuiCssBaseline: {
          styleOverrides: {
            body: {
              direction: isRTL ? 'rtl' : 'ltr',
            },
          },
        },
      },
    }, locale);
  };

  // دوال التحكم في الثيم
  const toggleTheme = () => {
    dispatch(toggleThemeMode());
  };

  const setThemeMode = (mode: 'light' | 'dark' | 'auto') => {
    dispatch(updateThemeSettings({ mode }));
  };

  const setPrimaryColor = (color: string) => {
    dispatch(updateThemeSettings({ primaryColor: color }));
  };

  const setSecondaryColor = (color: string) => {
    dispatch(updateThemeSettings({ secondaryColor: color }));
  };

  const setFontSize = (size: 'small' | 'medium' | 'large') => {
    dispatch(updateThemeSettings({ fontSize: size }));
  };

  // دوال التحكم في النظام
  const setLanguage = (language: 'ar' | 'en') => {
    dispatch(updateLanguage(language));
    
    // تحديث اتجاه الصفحة
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
  };

  const setCurrency = (currency: string) => {
    dispatch(updateCurrency(currency));
  };

  const setTimezone = (timezone: string) => {
    dispatch(updateSystemSettings({ timezone }));
  };

  const setDateFormat = (format: string) => {
    dispatch(updateSystemSettings({ dateFormat: format }));
  };

  // دوال التنسيق
  const formatCurrency = (amount: number): string => {
    const currency = systemSettings.defaultCurrency;
    const locale = systemSettings.defaultLanguage === 'ar' ? 'ar-SA' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (date: Date | string): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const locale = systemSettings.defaultLanguage === 'ar' ? 'ar-SA' : 'en-US';
    
    return dateObj.toLocaleDateString(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const formatTime = (date: Date | string): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const locale = systemSettings.defaultLanguage === 'ar' ? 'ar-SA' : 'en-US';
    
    return dateObj.toLocaleTimeString(locale, {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // مراقبة تغييرات الثيم التلقائي
  useEffect(() => {
    if (themeSettings.mode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = () => {
        // إعادة إنشاء الثيم عند تغيير تفضيل النظام
        forceUpdate();
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [themeSettings.mode]);

  // إجبار إعادة الرسم
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);

  // تطبيق إعدادات اللغة على الصفحة
  useEffect(() => {
    const isRTL = systemSettings.defaultLanguage === 'ar';
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = systemSettings.defaultLanguage;
  }, [systemSettings.defaultLanguage]);

  // تطبيق إعدادات الخط
  useEffect(() => {
    const fontSize = themeSettings.fontSize === 'small' ? '12px' : 
                    themeSettings.fontSize === 'large' ? '16px' : '14px';
    document.documentElement.style.fontSize = fontSize;
  }, [themeSettings.fontSize]);

  // قيم Context
  const contextValue: SettingsContextType = {
    // إعدادات الثيم
    themeMode: themeSettings.mode,
    primaryColor: themeSettings.primaryColor,
    secondaryColor: themeSettings.secondaryColor,
    fontSize: themeSettings.fontSize,
    
    // إعدادات النظام
    language: systemSettings.defaultLanguage,
    currency: systemSettings.defaultCurrency,
    timezone: systemSettings.timezone,
    dateFormat: systemSettings.dateFormat,
    
    // دوال التحكم
    toggleTheme,
    setThemeMode,
    setPrimaryColor,
    setSecondaryColor,
    setFontSize,
    setLanguage,
    setCurrency,
    setTimezone,
    setDateFormat,
    
    // دوال مساعدة
    formatCurrency,
    formatDate,
    formatTime,
    isRTL: systemSettings.defaultLanguage === 'ar',
  };

  const theme = createMuiTheme();
  const isRTL = systemSettings.defaultLanguage === 'ar';

  return (
    <SettingsContext.Provider value={contextValue}>
      <CacheProvider value={isRTL ? cacheRtl : cacheLtr}>
        <MuiThemeProvider theme={theme}>
          {children}
        </MuiThemeProvider>
      </CacheProvider>
    </SettingsContext.Provider>
  );
};

// Hook للحصول على إعدادات الثيم
export const useTheme = () => {
  const { themeMode, primaryColor, secondaryColor, fontSize, toggleTheme, setThemeMode, setPrimaryColor, setSecondaryColor, setFontSize } = useSettings();
  
  return {
    mode: themeMode,
    primaryColor,
    secondaryColor,
    fontSize,
    toggleTheme,
    setMode: setThemeMode,
    setPrimaryColor,
    setSecondaryColor,
    setFontSize,
  };
};

// Hook للحصول على إعدادات اللغة
export const useLocale = () => {
  const { language, isRTL, setLanguage } = useSettings();
  
  return {
    language,
    isRTL,
    setLanguage,
    t: (key: string) => {
      // دالة ترجمة بسيطة - يمكن تطويرها لاحقاً
      return key;
    },
  };
};

// Hook للحصول على إعدادات التنسيق
export const useFormatting = () => {
  const { currency, formatCurrency, formatDate, formatTime, setCurrency } = useSettings();
  
  return {
    currency,
    formatCurrency,
    formatDate,
    formatTime,
    setCurrency,
  };
};

export default SettingsContext;
