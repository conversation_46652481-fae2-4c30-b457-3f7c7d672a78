import React from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  IconButton,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  Inventory,
  ShoppingCart,
  AttachMoney,
  Warning,
  Notifications,
  MoreVert,
  Add,
  ArrowUpward,
  ArrowDownward,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

import { useAuth } from '@/contexts/AuthContext';
import { useSettings } from '@/contexts/SettingsContext';
import DashboardLayout from '@/components/layouts/DashboardLayout';

// تصميم بطاقة الإحصائيات
const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

// تصميم أيقونة الإحصائية
const StatsIcon = styled(Avatar)<{ color: string }>(({ theme, color }) => ({
  backgroundColor: color,
  width: 56,
  height: 56,
  marginBottom: theme.spacing(2),
}));

// بيانات وهمية للإحصائيات
const statsData = [
  {
    title: 'إجمالي المبيعات',
    value: '125,430',
    unit: 'ر.س',
    change: '+12.5%',
    trend: 'up',
    icon: AttachMoney,
    color: '#4caf50',
  },
  {
    title: 'العملاء',
    value: '1,234',
    unit: 'عميل',
    change: '+8.2%',
    trend: 'up',
    icon: People,
    color: '#2196f3',
  },
  {
    title: 'المنتجات',
    value: '567',
    unit: 'منتج',
    change: '+3.1%',
    trend: 'up',
    icon: Inventory,
    color: '#ff9800',
  },
  {
    title: 'الطلبات',
    value: '89',
    unit: 'طلب',
    change: '-2.4%',
    trend: 'down',
    icon: ShoppingCart,
    color: '#f44336',
  },
];

// بيانات وهمية للأنشطة الأخيرة
const recentActivities = [
  {
    id: 1,
    type: 'sale',
    title: 'فاتورة بيع جديدة',
    description: 'فاتورة رقم #INV-001 للعميل أحمد محمد',
    amount: '2,500 ر.س',
    time: 'منذ 5 دقائق',
    status: 'completed',
  },
  {
    id: 2,
    type: 'customer',
    title: 'عميل جديد',
    description: 'تم إضافة العميل: شركة التقنية المتقدمة',
    time: 'منذ 15 دقيقة',
    status: 'new',
  },
  {
    id: 3,
    type: 'inventory',
    title: 'تنبيه مخزون',
    description: 'المنتج "لابتوب ديل" أوشك على النفاد',
    time: 'منذ 30 دقيقة',
    status: 'warning',
  },
  {
    id: 4,
    type: 'payment',
    title: 'دفعة مستلمة',
    description: 'دفعة من العميل محمد أحمد بقيمة 1,200 ر.س',
    amount: '1,200 ر.س',
    time: 'منذ ساعة',
    status: 'completed',
  },
];

// بيانات وهمية للمهام المعلقة
const pendingTasks = [
  {
    id: 1,
    title: 'مراجعة فواتير الشهر',
    priority: 'high',
    dueDate: 'اليوم',
    progress: 75,
  },
  {
    id: 2,
    title: 'تحديث أسعار المنتجات',
    priority: 'medium',
    dueDate: 'غداً',
    progress: 30,
  },
  {
    id: 3,
    title: 'إعداد تقرير المبيعات',
    priority: 'low',
    dueDate: 'هذا الأسبوع',
    progress: 10,
  },
];

const DashboardPage: NextPage = () => {
  const { user } = useAuth();
  const { formatCurrency, formatDate } = useSettings();

  return (
    <>
      <Head>
        <title>لوحة التحكم - نظام الوكالة التجارية المتكامل</title>
        <meta name="description" content="لوحة التحكم الرئيسية لنظام الوكالة التجارية المتكامل" />
      </Head>

      <DashboardLayout>
        <Box sx={{ flexGrow: 1, p: 3 }}>
          {/* ترحيب */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              مرحباً، {user?.firstName} 👋
            </Typography>
            <Typography variant="body1" color="text.secondary">
              إليك نظرة عامة على أداء نشاطك التجاري اليوم
            </Typography>
          </Box>

          {/* بطاقات الإحصائيات */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {statsData.map((stat, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <StatsCard>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <StatsIcon color={stat.color}>
                        <stat.icon />
                      </StatsIcon>
                      <IconButton size="small">
                        <MoreVert />
                      </IconButton>
                    </Box>
                    
                    <Typography variant="h4" component="div" gutterBottom>
                      {stat.value}
                      <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        {stat.unit}
                      </Typography>
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {stat.title}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      {stat.trend === 'up' ? (
                        <ArrowUpward sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                      ) : (
                        <ArrowDownward sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
                      )}
                      <Typography
                        variant="body2"
                        sx={{
                          color: stat.trend === 'up' ? 'success.main' : 'error.main',
                          fontWeight: 'medium',
                        }}
                      >
                        {stat.change}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        من الشهر الماضي
                      </Typography>
                    </Box>
                  </CardContent>
                </StatsCard>
              </Grid>
            ))}
          </Grid>

          <Grid container spacing={3}>
            {/* الأنشطة الأخيرة */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" component="h2">
                      الأنشطة الأخيرة
                    </Typography>
                    <Button size="small" endIcon={<Add />}>
                      عرض الكل
                    </Button>
                  </Box>
                  
                  <List>
                    {recentActivities.map((activity, index) => (
                      <ListItem
                        key={activity.id}
                        divider={index < recentActivities.length - 1}
                        sx={{ px: 0 }}
                      >
                        <ListItemIcon>
                          {activity.type === 'sale' && <AttachMoney color="success" />}
                          {activity.type === 'customer' && <People color="primary" />}
                          {activity.type === 'inventory' && <Warning color="warning" />}
                          {activity.type === 'payment' && <TrendingUp color="success" />}
                        </ListItemIcon>
                        <ListItemText
                          primary={activity.title}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {activity.description}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 0.5 }}>
                                <Typography variant="caption" color="text.secondary">
                                  {activity.time}
                                </Typography>
                                {activity.amount && (
                                  <Typography variant="body2" fontWeight="medium" color="success.main">
                                    {activity.amount}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          }
                        />
                        <Chip
                          label={
                            activity.status === 'completed' ? 'مكتمل' :
                            activity.status === 'new' ? 'جديد' :
                            activity.status === 'warning' ? 'تحذير' : activity.status
                          }
                          size="small"
                          color={
                            activity.status === 'completed' ? 'success' :
                            activity.status === 'new' ? 'primary' :
                            activity.status === 'warning' ? 'warning' : 'default'
                          }
                          variant="outlined"
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* المهام المعلقة */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" component="h2">
                      المهام المعلقة
                    </Typography>
                    <IconButton size="small">
                      <Add />
                    </IconButton>
                  </Box>
                  
                  <List>
                    {pendingTasks.map((task, index) => (
                      <ListItem key={task.id} divider={index < pendingTasks.length - 1} sx={{ px: 0 }}>
                        <ListItemText
                          primary={task.title}
                          secondary={
                            <Box sx={{ mt: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="caption" color="text.secondary">
                                  {task.dueDate}
                                </Typography>
                                <Chip
                                  label={
                                    task.priority === 'high' ? 'عالي' :
                                    task.priority === 'medium' ? 'متوسط' : 'منخفض'
                                  }
                                  size="small"
                                  color={
                                    task.priority === 'high' ? 'error' :
                                    task.priority === 'medium' ? 'warning' : 'default'
                                  }
                                  variant="outlined"
                                />
                              </Box>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={task.progress}
                                  sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                                />
                                <Typography variant="caption" color="text.secondary">
                                  {task.progress}%
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                  
                  <Button fullWidth variant="outlined" sx={{ mt: 2 }}>
                    عرض جميع المهام
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </DashboardLayout>
    </>
  );
};

export default DashboardPage;
