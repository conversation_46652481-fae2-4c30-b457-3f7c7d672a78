const { sequelize } = require('../config/database');

// استيراد جميع النماذج
const User = require('./User');
const Role = require('./Role');
const Customer = require('./Customer');
const Supplier = require('./Supplier');
const Product = require('./Product');
const { Inventory, InventoryMovement } = require('./Inventory');

// استيراد النماذج الإضافية (سيتم إنشاؤها لاحقاً)
// const ProductCategory = require('./ProductCategory');
// const Brand = require('./Brand');
// const Unit = require('./Unit');
// const Warehouse = require('./Warehouse');
// const PurchaseOrder = require('./PurchaseOrder');
// const PurchaseInvoice = require('./PurchaseInvoice');
// const SalesInvoice = require('./SalesInvoice');
// const Quotation = require('./Quotation');
// const Payment = require('./Payment');

// تعريف العلاقات بين النماذج

// علاقات المستخدمين والأدوار
User.belongsTo(Role, { 
  foreignKey: 'role_id', 
  as: 'role' 
});
Role.hasMany(User, { 
  foreignKey: 'role_id', 
  as: 'users' 
});

// علاقات المنتجات
// Product.belongsTo(ProductCategory, { 
//   foreignKey: 'category_id', 
//   as: 'category' 
// });
// Product.belongsTo(Brand, { 
//   foreignKey: 'brand_id', 
//   as: 'brand' 
// });
// Product.belongsTo(Unit, { 
//   foreignKey: 'unit_id', 
//   as: 'unit' 
// });

// علاقات المخزون
Product.hasMany(Inventory, { 
  foreignKey: 'product_id', 
  as: 'inventory' 
});
Inventory.belongsTo(Product, { 
  foreignKey: 'product_id', 
  as: 'product' 
});

Product.hasMany(InventoryMovement, { 
  foreignKey: 'product_id', 
  as: 'movements' 
});
InventoryMovement.belongsTo(Product, { 
  foreignKey: 'product_id', 
  as: 'product' 
});

// علاقات المستخدمين مع البيانات
User.hasMany(Customer, { 
  foreignKey: 'created_by', 
  as: 'customers' 
});
Customer.belongsTo(User, { 
  foreignKey: 'created_by', 
  as: 'creator' 
});

User.hasMany(Supplier, { 
  foreignKey: 'created_by', 
  as: 'suppliers' 
});
Supplier.belongsTo(User, { 
  foreignKey: 'created_by', 
  as: 'creator' 
});

User.hasMany(Product, { 
  foreignKey: 'created_by', 
  as: 'products' 
});
Product.belongsTo(User, { 
  foreignKey: 'created_by', 
  as: 'creator' 
});

User.hasMany(InventoryMovement, { 
  foreignKey: 'created_by', 
  as: 'inventory_movements' 
});
InventoryMovement.belongsTo(User, { 
  foreignKey: 'created_by', 
  as: 'creator' 
});

// دالة لمزامنة قاعدة البيانات
const syncDatabase = async (options = {}) => {
  try {
    console.log('🔄 بدء مزامنة قاعدة البيانات...');
    
    // مزامنة النماذج بالترتيب الصحيح
    await Role.sync(options);
    console.log('✅ تم مزامنة جدول الأدوار');
    
    await User.sync(options);
    console.log('✅ تم مزامنة جدول المستخدمين');
    
    await Customer.sync(options);
    console.log('✅ تم مزامنة جدول العملاء');
    
    await Supplier.sync(options);
    console.log('✅ تم مزامنة جدول الموردين');
    
    await Product.sync(options);
    console.log('✅ تم مزامنة جدول المنتجات');
    
    await Inventory.sync(options);
    console.log('✅ تم مزامنة جدول المخزون');
    
    await InventoryMovement.sync(options);
    console.log('✅ تم مزامنة جدول حركات المخزون');
    
    console.log('🎉 تم مزامنة جميع الجداول بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في مزامنة قاعدة البيانات:', error.message);
    throw error;
  }
};

// دالة لإنشاء البيانات الأساسية
const seedDatabase = async () => {
  try {
    console.log('🌱 بدء إنشاء البيانات الأساسية...');
    
    // إنشاء الأدوار الافتراضية
    await Role.seedDefaultRoles();
    console.log('✅ تم إنشاء الأدوار الافتراضية');
    
    // إنشاء المستخدم الافتراضي
    const adminRole = await Role.findByName('admin');
    if (adminRole) {
      const existingAdmin = await User.findByUsername('admin');
      if (!existingAdmin) {
        await User.createUser({
          username: 'admin',
          email: '<EMAIL>',
          password_hash: 'Admin@123456',
          first_name: 'مدير',
          last_name: 'النظام',
          role_id: adminRole.id,
          is_active: true,
          email_verified: true
        });
        console.log('✅ تم إنشاء المستخدم الافتراضي');
      }
    }
    
    console.log('🎉 تم إنشاء البيانات الأساسية بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الأساسية:', error.message);
    throw error;
  }
};

// دالة لإعادة تعيين قاعدة البيانات
const resetDatabase = async () => {
  try {
    console.log('🔄 بدء إعادة تعيين قاعدة البيانات...');
    
    await sequelize.drop();
    console.log('✅ تم حذف جميع الجداول');
    
    await syncDatabase({ force: true });
    console.log('✅ تم إعادة إنشاء الجداول');
    
    await seedDatabase();
    console.log('✅ تم إنشاء البيانات الأساسية');
    
    console.log('🎉 تم إعادة تعيين قاعدة البيانات بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين قاعدة البيانات:', error.message);
    throw error;
  }
};

// دالة للحصول على إحصائيات قاعدة البيانات
const getDatabaseStats = async () => {
  try {
    const stats = {
      users: await User.count(),
      roles: await Role.count(),
      customers: await Customer.count({ where: { is_active: true } }),
      suppliers: await Supplier.count({ where: { is_active: true } }),
      products: await Product.count({ where: { is_active: true } }),
      inventory_items: await Inventory.count(),
      inventory_movements: await InventoryMovement.count()
    };
    
    return stats;
  } catch (error) {
    console.error('❌ خطأ في الحصول على إحصائيات قاعدة البيانات:', error.message);
    throw error;
  }
};

// دالة للتحقق من صحة قاعدة البيانات
const validateDatabase = async () => {
  try {
    console.log('🔍 بدء التحقق من صحة قاعدة البيانات...');
    
    // التحقق من وجود الجداول الأساسية
    const tables = await sequelize.getQueryInterface().showAllTables();
    const requiredTables = ['roles', 'users', 'customers', 'suppliers', 'products', 'inventory', 'inventory_movements'];
    
    for (const table of requiredTables) {
      if (!tables.includes(table)) {
        throw new Error(`الجدول المطلوب غير موجود: ${table}`);
      }
    }
    
    // التحقق من وجود البيانات الأساسية
    const adminRole = await Role.findByName('admin');
    if (!adminRole) {
      throw new Error('الدور الافتراضي للمدير غير موجود');
    }
    
    const adminUser = await User.findByUsername('admin');
    if (!adminUser) {
      throw new Error('المستخدم الافتراضي للمدير غير موجود');
    }
    
    console.log('✅ قاعدة البيانات صحيحة ومكتملة');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من صحة قاعدة البيانات:', error.message);
    return false;
  }
};

// تصدير النماذج والدوال
module.exports = {
  // النماذج
  sequelize,
  User,
  Role,
  Customer,
  Supplier,
  Product,
  Inventory,
  InventoryMovement,
  
  // الدوال
  syncDatabase,
  seedDatabase,
  resetDatabase,
  getDatabaseStats,
  validateDatabase
};
