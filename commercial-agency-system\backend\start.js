const app = require('./src/app');
const { sequelize } = require('./src/config/database');

const PORT = process.env.PORT || 5000;

async function startServer() {
  try {
    // اختبار الاتصال بقاعدة البيانات
    await sequelize.authenticate();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // مزامنة النماذج مع قاعدة البيانات
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('✅ تم مزامنة النماذج مع قاعدة البيانات');
    }

    // بدء تشغيل الخادم
    app.listen(PORT, () => {
      console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
      console.log(`📖 توثيق API متاح على: http://localhost:${PORT}/api-docs`);
      console.log(`🏥 فحص الصحة متاح على: http://localhost:${PORT}/health`);
    });

  } catch (error) {
    console.error('❌ خطأ في بدء تشغيل الخادم:', error);
    process.exit(1);
  }
}

// التعامل مع إشارات النظام
process.on('SIGTERM', async () => {
  console.log('📴 تم استلام إشارة SIGTERM، جاري إغلاق الخادم...');
  await sequelize.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('📴 تم استلام إشارة SIGINT، جاري إغلاق الخادم...');
  await sequelize.close();
  process.exit(0);
});

startServer();
