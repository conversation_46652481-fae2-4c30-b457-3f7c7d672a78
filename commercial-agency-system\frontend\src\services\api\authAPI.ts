import { apiClient } from './client';

// تعريف أنواع البيانات
export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: {
    id: number;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    fullName: string;
    avatar?: string;
    role: {
      id: number;
      name: string;
      nameAr: string;
      permissions: string[];
    };
    lastLogin?: string;
  };
  accessToken: string;
  expiresIn: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ResetPasswordData {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: string;
}

// خدمة API للمصادقة
export const authAPI = {
  // تسجيل الدخول
  login: async (credentials: LoginCredentials) => {
    const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
    return response;
  },

  // تسجيل الخروج
  logout: async () => {
    const response = await apiClient.post('/auth/logout');
    return response;
  },

  // تحديث الرمز المميز
  refreshToken: async () => {
    const response = await apiClient.post<RefreshTokenResponse>('/auth/refresh');
    return response;
  },

  // التحقق من حالة المصادقة
  checkAuth: async () => {
    const response = await apiClient.get<{ user: LoginResponse['user'] }>('/auth/me');
    return response;
  },

  // تغيير كلمة المرور
  changePassword: async (data: ChangePasswordData) => {
    const response = await apiClient.post('/auth/change-password', data);
    return response;
  },

  // طلب إعادة تعيين كلمة المرور
  forgotPassword: async (email: string) => {
    const response = await apiClient.post('/auth/forgot-password', { email });
    return response;
  },

  // إعادة تعيين كلمة المرور
  resetPassword: async (data: ResetPasswordData) => {
    const response = await apiClient.post('/auth/reset-password', data);
    return response;
  },

  // التحقق من صحة الرمز المميز
  validateToken: async (token: string) => {
    const response = await apiClient.post('/auth/validate-token', { token });
    return response;
  },

  // إلغاء جميع الجلسات
  revokeAllSessions: async () => {
    const response = await apiClient.post('/auth/revoke-all-sessions');
    return response;
  },

  // الحصول على الجلسات النشطة
  getActiveSessions: async () => {
    const response = await apiClient.get('/auth/active-sessions');
    return response;
  },

  // إلغاء جلسة محددة
  revokeSession: async (sessionId: string) => {
    const response = await apiClient.delete(`/auth/sessions/${sessionId}`);
    return response;
  },

  // تفعيل المصادقة الثنائية
  enableTwoFactor: async (method: 'sms' | 'email' | 'app') => {
    const response = await apiClient.post('/auth/two-factor/enable', { method });
    return response;
  },

  // إلغاء تفعيل المصادقة الثنائية
  disableTwoFactor: async () => {
    const response = await apiClient.post('/auth/two-factor/disable');
    return response;
  },

  // التحقق من رمز المصادقة الثنائية
  verifyTwoFactor: async (code: string) => {
    const response = await apiClient.post('/auth/two-factor/verify', { code });
    return response;
  },

  // إرسال رمز المصادقة الثنائية
  sendTwoFactorCode: async () => {
    const response = await apiClient.post('/auth/two-factor/send-code');
    return response;
  },

  // الحصول على رموز الاسترداد للمصادقة الثنائية
  getRecoveryCodes: async () => {
    const response = await apiClient.get('/auth/two-factor/recovery-codes');
    return response;
  },

  // إنشاء رموز استرداد جديدة
  generateRecoveryCodes: async () => {
    const response = await apiClient.post('/auth/two-factor/generate-recovery-codes');
    return response;
  },

  // استخدام رمز الاسترداد
  useRecoveryCode: async (code: string) => {
    const response = await apiClient.post('/auth/two-factor/use-recovery-code', { code });
    return response;
  },
};

// دوال مساعدة للمصادقة
export const authHelpers = {
  // التحقق من انتهاء صلاحية الرمز المميز
  isTokenExpired: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  },

  // استخراج معلومات المستخدم من الرمز المميز
  getUserFromToken: (token: string) => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.id,
        username: payload.username,
        email: payload.email,
        role: payload.role,
        permissions: payload.permissions,
      };
    } catch {
      return null;
    }
  },

  // التحقق من الصلاحية
  hasPermission: (permissions: string[], requiredPermission: string): boolean => {
    return permissions.includes('admin.all') || permissions.includes(requiredPermission);
  },

  // التحقق من الدور
  hasRole: (userRole: string, requiredRoles: string[]): boolean => {
    return userRole === 'admin' || requiredRoles.includes(userRole);
  },

  // تنسيق رسائل الخطأ
  formatAuthError: (error: any): string => {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    
    switch (error.response?.status) {
      case 401:
        return 'اسم المستخدم أو كلمة المرور غير صحيحة';
      case 403:
        return 'ليس لديك صلاحية للوصول';
      case 423:
        return 'الحساب مقفل مؤقتاً';
      case 429:
        return 'تم تجاوز عدد المحاولات المسموحة';
      case 500:
        return 'خطأ في الخادم، يرجى المحاولة لاحقاً';
      default:
        return 'حدث خطأ غير متوقع';
    }
  },

  // حفظ الرمز المميز في التخزين المحلي
  saveToken: (token: string) => {
    localStorage.setItem('accessToken', token);
  },

  // الحصول على الرمز المميز من التخزين المحلي
  getToken: (): string | null => {
    return localStorage.getItem('accessToken');
  },

  // حذف الرمز المميز من التخزين المحلي
  removeToken: () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  },

  // حفظ رمز التحديث
  saveRefreshToken: (refreshToken: string) => {
    localStorage.setItem('refreshToken', refreshToken);
  },

  // الحصول على رمز التحديث
  getRefreshToken: (): string | null => {
    return localStorage.getItem('refreshToken');
  },
};

export default authAPI;
