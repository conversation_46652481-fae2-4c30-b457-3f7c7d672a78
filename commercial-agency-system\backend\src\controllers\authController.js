const { User, Role } = require('../models');
const { 
  generateToken, 
  generateRefreshToken, 
  verifyRefreshToken,
  validatePasswordStrength,
  generateVerificationCode,
  generateResetToken
} = require('../config/auth');
const { validationResult } = require('express-validator');

// تسجيل الدخول
const login = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const { username, password, rememberMe } = req.body;

    // البحث عن المستخدم
    const user = await User.findOne({
      where: { 
        $or: [
          { username: username },
          { email: username }
        ],
        is_active: true 
      },
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar', 'permissions']
      }]
    });

    if (!user) {
      return res.error('اسم المستخدم أو كلمة المرور غير صحيحة', 401);
    }

    // التحقق من قفل الحساب
    if (user.isLocked()) {
      const lockTime = Math.ceil((user.locked_until - new Date()) / (1000 * 60));
      return res.error(`الحساب مقفل لمدة ${lockTime} دقيقة`, 423);
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await user.checkPassword(password);
    if (!isPasswordValid) {
      await user.incrementLoginAttempts();
      return res.error('اسم المستخدم أو كلمة المرور غير صحيحة', 401);
    }

    // إعادة تعيين محاولات تسجيل الدخول
    await user.resetLoginAttempts();

    // إنشاء الرموز المميزة
    const tokenPayload = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role.name,
      permissions: user.role.permissions
    };

    const accessToken = generateToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // حفظ جلسة المستخدم
    req.session.userId = user.id;
    req.session.username = user.username;

    // إعداد ملف تعريف الارتباط
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 7 أيام أو 24 ساعة
    };

    res.cookie('refreshToken', refreshToken, cookieOptions);

    res.success({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        fullName: user.getFullName(),
        avatar: user.avatar,
        role: {
          id: user.role.id,
          name: user.role.name,
          nameAr: user.role.name_ar,
          permissions: user.role.permissions
        },
        lastLogin: user.last_login
      },
      accessToken,
      expiresIn: process.env.JWT_EXPIRE || '24h'
    }, 'تم تسجيل الدخول بنجاح');

  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    res.error('حدث خطأ في تسجيل الدخول', 500);
  }
};

// تسجيل الخروج
const logout = async (req, res) => {
  try {
    // حذف الجلسة
    req.session.destroy((err) => {
      if (err) {
        console.error('خطأ في حذف الجلسة:', err);
      }
    });

    // حذف ملف تعريف الارتباط
    res.clearCookie('refreshToken');

    res.success(null, 'تم تسجيل الخروج بنجاح');

  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    res.error('حدث خطأ في تسجيل الخروج', 500);
  }
};

// تحديث الرمز المميز
const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.cookies;

    if (!refreshToken) {
      return res.error('رمز التحديث غير موجود', 401);
    }

    // التحقق من صحة رمز التحديث
    const decoded = verifyRefreshToken(refreshToken);

    // البحث عن المستخدم
    const user = await User.findByPk(decoded.id, {
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar', 'permissions']
      }]
    });

    if (!user || !user.is_active) {
      return res.error('المستخدم غير موجود أو غير نشط', 401);
    }

    // إنشاء رمز مميز جديد
    const tokenPayload = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role.name,
      permissions: user.role.permissions
    };

    const newAccessToken = generateToken(tokenPayload);

    res.success({
      accessToken: newAccessToken,
      expiresIn: process.env.JWT_EXPIRE || '24h'
    }, 'تم تحديث الرمز المميز بنجاح');

  } catch (error) {
    console.error('خطأ في تحديث الرمز المميز:', error);
    res.error('رمز التحديث غير صحيح أو منتهي الصلاحية', 401);
  }
};

// التحقق من حالة المصادقة
const checkAuth = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar', 'permissions']
      }]
    });

    if (!user || !user.is_active) {
      return res.error('المستخدم غير موجود أو غير نشط', 401);
    }

    res.success({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        fullName: user.getFullName(),
        avatar: user.avatar,
        role: {
          id: user.role.id,
          name: user.role.name,
          nameAr: user.role.name_ar,
          permissions: user.role.permissions
        },
        lastLogin: user.last_login
      }
    }, 'المستخدم مصادق عليه');

  } catch (error) {
    console.error('خطأ في التحقق من المصادقة:', error);
    res.error('حدث خطأ في التحقق من المصادقة', 500);
  }
};

// تغيير كلمة المرور
const changePassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // البحث عن المستخدم
    const user = await User.findByPk(userId);
    if (!user) {
      return res.error('المستخدم غير موجود', 404);
    }

    // التحقق من كلمة المرور الحالية
    const isCurrentPasswordValid = await user.checkPassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.error('كلمة المرور الحالية غير صحيحة', 400);
    }

    // التحقق من قوة كلمة المرور الجديدة
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      return res.error('كلمة المرور الجديدة غير قوية بما فيه الكفاية', 400, passwordValidation.errors);
    }

    // تحديث كلمة المرور
    await user.update({ password_hash: newPassword });

    res.success(null, 'تم تغيير كلمة المرور بنجاح');

  } catch (error) {
    console.error('خطأ في تغيير كلمة المرور:', error);
    res.error('حدث خطأ في تغيير كلمة المرور', 500);
  }
};

// طلب إعادة تعيين كلمة المرور
const forgotPassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const { email } = req.body;

    // البحث عن المستخدم
    const user = await User.findByEmail(email);
    if (!user) {
      // لا نكشف عن عدم وجود المستخدم لأسباب أمنية
      return res.success(null, 'إذا كان البريد الإلكتروني موجود، فسيتم إرسال رابط إعادة التعيين');
    }

    // إنشاء رمز إعادة التعيين
    const resetToken = generateResetToken();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // ساعة واحدة

    // حفظ رمز إعادة التعيين (يحتاج إلى إضافة حقول في قاعدة البيانات)
    // await user.update({
    //   reset_password_token: resetToken,
    //   reset_password_expires: resetExpires
    // });

    // إرسال البريد الإلكتروني (يحتاج إلى تنفيذ خدمة البريد الإلكتروني)
    // await sendPasswordResetEmail(user.email, resetToken);

    res.success(null, 'إذا كان البريد الإلكتروني موجود، فسيتم إرسال رابط إعادة التعيين');

  } catch (error) {
    console.error('خطأ في طلب إعادة تعيين كلمة المرور:', error);
    res.error('حدث خطأ في طلب إعادة تعيين كلمة المرور', 500);
  }
};

// إعادة تعيين كلمة المرور
const resetPassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.error('بيانات غير صحيحة', 400, errors.array());
    }

    const { token, newPassword } = req.body;

    // البحث عن المستخدم برمز إعادة التعيين
    // const user = await User.findOne({
    //   where: {
    //     reset_password_token: token,
    //     reset_password_expires: { [Op.gt]: new Date() }
    //   }
    // });

    // if (!user) {
    //   return res.error('رمز إعادة التعيين غير صحيح أو منتهي الصلاحية', 400);
    // }

    // التحقق من قوة كلمة المرور الجديدة
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      return res.error('كلمة المرور الجديدة غير قوية بما فيه الكفاية', 400, passwordValidation.errors);
    }

    // تحديث كلمة المرور وحذف رمز إعادة التعيين
    // await user.update({
    //   password_hash: newPassword,
    //   reset_password_token: null,
    //   reset_password_expires: null
    // });

    res.success(null, 'تم إعادة تعيين كلمة المرور بنجاح');

  } catch (error) {
    console.error('خطأ في إعادة تعيين كلمة المرور:', error);
    res.error('حدث خطأ في إعادة تعيين كلمة المرور', 500);
  }
};

module.exports = {
  login,
  logout,
  refreshToken,
  checkAuth,
  changePassword,
  forgotPassword,
  resetPassword
};
