const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// API routes
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'API is working!',
    timestamp: new Date().toISOString()
  });
});

// Mock auth endpoint
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  if (username === 'admin' && password === 'Admin@123456') {
    res.json({
      success: true,
      data: {
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          firstName: 'مدير',
          lastName: 'النظام',
          fullName: 'مدير النظام',
          role: {
            id: 1,
            name: 'admin',
            nameAr: 'مدير النظام',
            permissions: ['admin.all']
          }
        },
        accessToken: 'mock-jwt-token-' + Date.now()
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
    });
  }
});

// Mock auth check
app.get('/api/auth/me', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    res.json({
      success: true,
      data: {
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          firstName: 'مدير',
          lastName: 'النظام',
          fullName: 'مدير النظام',
          role: {
            id: 1,
            name: 'admin',
            nameAr: 'مدير النظام',
            permissions: ['admin.all']
          }
        }
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'غير مصرح له'
    });
  }
});

// Mock logout
app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'تم تسجيل الخروج بنجاح'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود'
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'خطأ في الخادم'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🏥 فحص الصحة متاح على: http://localhost:${PORT}/health`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
});

module.exports = app;
