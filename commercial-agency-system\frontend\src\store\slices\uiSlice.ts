import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// تعريف أنواع البيانات
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  timestamp: number;
}

export interface Modal {
  id: string;
  type: string;
  props?: any;
  isOpen: boolean;
}

export interface Breadcrumb {
  label: string;
  href?: string;
  icon?: string;
}

export interface UIState {
  // إعدادات الشريط الجانبي
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  sidebarMobile: boolean;
  
  // إعدادات الثيم
  darkMode: boolean;
  primaryColor: string;
  
  // إعدادات التخطيط
  layoutDirection: 'rtl' | 'ltr';
  layoutDensity: 'comfortable' | 'compact' | 'spacious';
  
  // حالة التحميل العامة
  globalLoading: boolean;
  loadingMessage: string;
  
  // الإشعارات
  notifications: Notification[];
  
  // النوافذ المنبثقة
  modals: Modal[];
  
  // مسار التنقل
  breadcrumbs: Breadcrumb[];
  pageTitle: string;
  
  // حالة الشبكة
  isOnline: boolean;
  
  // إعدادات الجداول
  tableSettings: {
    pageSize: number;
    density: 'standard' | 'comfortable' | 'compact';
    showFilters: boolean;
  };
  
  // إعدادات النماذج
  formSettings: {
    autoSave: boolean;
    showValidationOnChange: boolean;
  };
  
  // حالة البحث العام
  globalSearch: {
    isOpen: boolean;
    query: string;
    results: any[];
    isLoading: boolean;
  };
  
  // إعدادات الطباعة
  printSettings: {
    orientation: 'portrait' | 'landscape';
    paperSize: 'A4' | 'A3' | 'Letter';
    margins: 'normal' | 'narrow' | 'wide';
  };
}

// الحالة الأولية
const initialState: UIState = {
  sidebarOpen: true,
  sidebarCollapsed: false,
  sidebarMobile: false,
  darkMode: false,
  primaryColor: '#2196f3',
  layoutDirection: 'rtl',
  layoutDensity: 'comfortable',
  globalLoading: false,
  loadingMessage: '',
  notifications: [],
  modals: [],
  breadcrumbs: [],
  pageTitle: '',
  isOnline: true,
  tableSettings: {
    pageSize: 10,
    density: 'standard',
    showFilters: false,
  },
  formSettings: {
    autoSave: true,
    showValidationOnChange: false,
  },
  globalSearch: {
    isOpen: false,
    query: '',
    results: [],
    isLoading: false,
  },
  printSettings: {
    orientation: 'portrait',
    paperSize: 'A4',
    margins: 'normal',
  },
};

// إنشاء الـ slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // إدارة الشريط الجانبي
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    
    toggleSidebarCollapsed: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    
    setSidebarMobile: (state, action: PayloadAction<boolean>) => {
      state.sidebarMobile = action.payload;
    },
    
    // إدارة الثيم
    toggleDarkMode: (state) => {
      state.darkMode = !state.darkMode;
    },
    
    setDarkMode: (state, action: PayloadAction<boolean>) => {
      state.darkMode = action.payload;
    },
    
    setPrimaryColor: (state, action: PayloadAction<string>) => {
      state.primaryColor = action.payload;
    },
    
    // إدارة التخطيط
    setLayoutDirection: (state, action: PayloadAction<'rtl' | 'ltr'>) => {
      state.layoutDirection = action.payload;
    },
    
    setLayoutDensity: (state, action: PayloadAction<'comfortable' | 'compact' | 'spacious'>) => {
      state.layoutDensity = action.payload;
    },
    
    // إدارة التحميل العام
    setGlobalLoading: (state, action: PayloadAction<{ loading: boolean; message?: string }>) => {
      state.globalLoading = action.payload.loading;
      state.loadingMessage = action.payload.message || '';
    },
    
    // إدارة الإشعارات
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: Date.now(),
      };
      state.notifications.push(notification);
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      );
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // إدارة النوافذ المنبثقة
    openModal: (state, action: PayloadAction<Omit<Modal, 'isOpen'>>) => {
      const existingModal = state.modals.find(modal => modal.id === action.payload.id);
      if (existingModal) {
        existingModal.isOpen = true;
        existingModal.props = action.payload.props;
      } else {
        state.modals.push({ ...action.payload, isOpen: true });
      }
    },
    
    closeModal: (state, action: PayloadAction<string>) => {
      const modal = state.modals.find(modal => modal.id === action.payload);
      if (modal) {
        modal.isOpen = false;
      }
    },
    
    closeAllModals: (state) => {
      state.modals.forEach(modal => {
        modal.isOpen = false;
      });
    },
    
    // إدارة مسار التنقل
    setBreadcrumbs: (state, action: PayloadAction<Breadcrumb[]>) => {
      state.breadcrumbs = action.payload;
    },
    
    addBreadcrumb: (state, action: PayloadAction<Breadcrumb>) => {
      state.breadcrumbs.push(action.payload);
    },
    
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload;
    },
    
    // إدارة حالة الشبكة
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    
    // إدارة إعدادات الجداول
    setTableSettings: (state, action: PayloadAction<Partial<UIState['tableSettings']>>) => {
      state.tableSettings = { ...state.tableSettings, ...action.payload };
    },
    
    // إدارة إعدادات النماذج
    setFormSettings: (state, action: PayloadAction<Partial<UIState['formSettings']>>) => {
      state.formSettings = { ...state.formSettings, ...action.payload };
    },
    
    // إدارة البحث العام
    setGlobalSearchOpen: (state, action: PayloadAction<boolean>) => {
      state.globalSearch.isOpen = action.payload;
      if (!action.payload) {
        state.globalSearch.query = '';
        state.globalSearch.results = [];
      }
    },
    
    setGlobalSearchQuery: (state, action: PayloadAction<string>) => {
      state.globalSearch.query = action.payload;
    },
    
    setGlobalSearchResults: (state, action: PayloadAction<any[]>) => {
      state.globalSearch.results = action.payload;
    },
    
    setGlobalSearchLoading: (state, action: PayloadAction<boolean>) => {
      state.globalSearch.isLoading = action.payload;
    },
    
    // إدارة إعدادات الطباعة
    setPrintSettings: (state, action: PayloadAction<Partial<UIState['printSettings']>>) => {
      state.printSettings = { ...state.printSettings, ...action.payload };
    },
    
    // إعادة تعيين واجهة المستخدم
    resetUI: (state) => {
      return { ...initialState, isOnline: state.isOnline };
    },
  },
});

// تصدير الإجراءات
export const {
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapsed,
  setSidebarCollapsed,
  setSidebarMobile,
  toggleDarkMode,
  setDarkMode,
  setPrimaryColor,
  setLayoutDirection,
  setLayoutDensity,
  setGlobalLoading,
  addNotification,
  removeNotification,
  clearNotifications,
  openModal,
  closeModal,
  closeAllModals,
  setBreadcrumbs,
  addBreadcrumb,
  setPageTitle,
  setOnlineStatus,
  setTableSettings,
  setFormSettings,
  setGlobalSearchOpen,
  setGlobalSearchQuery,
  setGlobalSearchResults,
  setGlobalSearchLoading,
  setPrintSettings,
  resetUI,
} = uiSlice.actions;

// Selectors
export const selectUI = (state: { ui: UIState }) => state.ui;
export const selectSidebar = (state: { ui: UIState }) => ({
  open: state.ui.sidebarOpen,
  collapsed: state.ui.sidebarCollapsed,
  mobile: state.ui.sidebarMobile,
});
export const selectTheme = (state: { ui: UIState }) => ({
  darkMode: state.ui.darkMode,
  primaryColor: state.ui.primaryColor,
});
export const selectLayout = (state: { ui: UIState }) => ({
  direction: state.ui.layoutDirection,
  density: state.ui.layoutDensity,
});
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectBreadcrumbs = (state: { ui: UIState }) => state.ui.breadcrumbs;
export const selectGlobalSearch = (state: { ui: UIState }) => state.ui.globalSearch;

// دوال مساعدة للإشعارات
export const createNotification = (
  type: Notification['type'],
  title: string,
  message: string,
  options?: Partial<Pick<Notification, 'duration' | 'persistent'>>
) => ({
  type,
  title,
  message,
  duration: options?.duration || 5000,
  persistent: options?.persistent || false,
});

// تصدير المقلل
export default uiSlice.reducer;
