import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';

// إعدادات API
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

// إنشاء عميل Axios
const apiClient: AxiosInstance = axios.create(API_CONFIG);

// متغيرات للتحكم في تحديث الرمز المميز
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (reason?: any) => void;
}> = [];

// دالة معالجة طابور الطلبات المعلقة
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// دالة الحصول على الرمز المميز من التخزين المحلي
const getToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('accessToken');
  }
  return null;
};

// دالة حفظ الرمز المميز في التخزين المحلي
const setToken = (token: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('accessToken', token);
  }
};

// دالة حذف الرمز المميز من التخزين المحلي
const removeToken = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }
};

// دالة تحديث الرمز المميز
const refreshToken = async (): Promise<string> => {
  try {
    const response = await axios.post(`${API_CONFIG.baseURL}/auth/refresh`, {}, {
      withCredentials: true,
    });
    
    const { accessToken } = response.data.data;
    setToken(accessToken);
    return accessToken;
  } catch (error) {
    removeToken();
    // إعادة توجيه إلى صفحة تسجيل الدخول
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login';
    }
    throw error;
  }
};

// Interceptor للطلبات (Request Interceptor)
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // إضافة الرمز المميز إلى الهيدر
    const token = getToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // إضافة معرف الطلب للتتبع
    config.headers = {
      ...config.headers,
      'X-Request-ID': `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };

    // إضافة الطابع الزمني
    config.metadata = { startTime: new Date() };

    return config;
  },
  (error: AxiosError) => {
    console.error('خطأ في إعداد الطلب:', error);
    return Promise.reject(error);
  }
);

// Interceptor للاستجابات (Response Interceptor)
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // حساب وقت الاستجابة
    const endTime = new Date();
    const startTime = response.config.metadata?.startTime;
    if (startTime) {
      const duration = endTime.getTime() - startTime.getTime();
      console.log(`API Response Time: ${duration}ms for ${response.config.url}`);
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    // معالجة خطأ 401 (غير مصرح له)
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // إضافة الطلب إلى الطابور
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const newToken = await refreshToken();
        processQueue(null, newToken);
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError, null);
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // معالجة الأخطاء الأخرى
    handleApiError(error);
    return Promise.reject(error);
  }
);

// دالة معالجة أخطاء API
const handleApiError = (error: AxiosError) => {
  const status = error.response?.status;
  const message = (error.response?.data as any)?.message || error.message;

  // عدم عرض إشعارات للأخطاء المعينة
  const silentErrors = [401, 403];
  if (silentErrors.includes(status || 0)) {
    return;
  }

  // عرض رسائل خطأ مخصصة
  switch (status) {
    case 400:
      toast.error('بيانات غير صحيحة');
      break;
    case 404:
      toast.error('المورد المطلوب غير موجود');
      break;
    case 422:
      toast.error('بيانات غير صالحة للمعالجة');
      break;
    case 429:
      toast.error('تم تجاوز عدد الطلبات المسموحة');
      break;
    case 500:
      toast.error('خطأ في الخادم');
      break;
    case 502:
      toast.error('الخادم غير متاح');
      break;
    case 503:
      toast.error('الخدمة غير متاحة مؤقتاً');
      break;
    default:
      if (message) {
        toast.error(message);
      } else {
        toast.error('حدث خطأ غير متوقع');
      }
  }
};

// دوال مساعدة للطلبات
export const apiHelpers = {
  // طلب GET
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.get<T>(url, config);
  },

  // طلب POST
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.post<T>(url, data, config);
  },

  // طلب PUT
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.put<T>(url, data, config);
  },

  // طلب PATCH
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.patch<T>(url, data, config);
  },

  // طلب DELETE
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.delete<T>(url, config);
  },

  // رفع ملف
  upload: async <T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<AxiosResponse<T>> => {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  },

  // تحميل ملف
  download: async (url: string, filename?: string): Promise<void> => {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  },

  // إلغاء الطلب
  createCancelToken: () => {
    return axios.CancelToken.source();
  },

  // التحقق من إلغاء الطلب
  isCancel: (error: any) => {
    return axios.isCancel(error);
  },
};

// دوال للتحكم في الرموز المميزة
export const tokenManager = {
  get: getToken,
  set: setToken,
  remove: removeToken,
  refresh: refreshToken,
};

// تصدير عميل API
export { apiClient };
export default apiClient;
