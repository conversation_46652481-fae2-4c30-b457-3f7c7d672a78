<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشتريات - نظام الوكالة التجارية المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="components/header.js"></script>
    <script src="components/crud.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
            margin: 0 0 5px 0;
        }

        .page-title p {
            color: #7f8c8d;
            margin: 0;
            font-size: 1rem;
        }

        .page-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 15px;
        }

        .stat-card .today .icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .stat-card .month .icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stat-card .year .icon { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .stat-card .total .icon { background: linear-gradient(135deg, #34495e, #2c3e50); }

        .stat-card h3 {
            color: #2c3e50;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .search-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .purchases-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #2c3e50;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-completed {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
        }

        .status-cancelled {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .search-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .page-actions {
                width: 100%;
                justify-content: center;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-title">
                <h1><i class="fas fa-file-invoice"></i> إدارة المشتريات</h1>
                <p>إدارة شاملة لجميع عمليات الشراء والفواتير</p>
            </div>
            <div class="page-actions">
                <a href="new-purchase.html" class="btn btn-success">
                    <i class="fas fa-plus"></i> فاتورة مشتريات جديدة
                </a>
                <button class="btn btn-secondary" onclick="exportPurchases()">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="stats-row">
            <div class="stat-card today">
                <div class="icon"><i class="fas fa-calendar-day"></i></div>
                <h3>مشتريات اليوم</h3>
                <div class="number" id="todayPurchases">0</div>
            </div>
            
            <div class="stat-card month">
                <div class="icon"><i class="fas fa-calendar-alt"></i></div>
                <h3>مشتريات الشهر</h3>
                <div class="number" id="monthPurchases">0</div>
            </div>
            
            <div class="stat-card year">
                <div class="icon"><i class="fas fa-calendar"></i></div>
                <h3>مشتريات السنة</h3>
                <div class="number" id="yearPurchases">0</div>
            </div>
            
            <div class="stat-card total">
                <div class="icon"><i class="fas fa-chart-line"></i></div>
                <h3>إجمالي المشتريات</h3>
                <div class="number" id="totalPurchases">0</div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-grid">
                <div class="form-group">
                    <label>رقم الفاتورة</label>
                    <input type="text" class="form-control" id="searchInvoice" placeholder="ادخل رقم الفاتورة">
                </div>
                <div class="form-group">
                    <label>المورد</label>
                    <input type="text" class="form-control" id="searchSupplier" placeholder="ادخل اسم المورد">
                </div>
                <div class="form-group">
                    <label>من تاريخ</label>
                    <input type="date" class="form-control" id="searchFromDate">
                </div>
                <div class="form-group">
                    <label>إلى تاريخ</label>
                    <input type="date" class="form-control" id="searchToDate">
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="searchPurchases()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Purchases Table -->
        <div class="purchases-table">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...
            </div>
            
            <div id="purchasesContent" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>المورد</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المتبقي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="purchasesTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <!-- أزرار التصفح -->
                </div>
            </div>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-file-invoice"></i>
                <h3>لا توجد فواتير مشتريات</h3>
                <p>لم يتم العثور على أي فواتير مشتريات. ابدأ بإنشاء فاتورة جديدة.</p>
            </div>
        </div>
    </div>

    <script>
        // Initialize CRUD Manager for purchases
        const crudManager = new CRUDManager({
            apiBaseUrl: '/api',
            itemsPerPage: 10,
            onDisplayData: displayPurchases
        });

        // Mock purchases data
        const mockPurchases = [
            {
                id: 2001,
                supplierName: 'شركة التقنية المتقدمة',
                supplierPhone: '0112345678',
                totalAmount: 15000,
                paidAmount: 15000,
                remainingAmount: 0,
                paymentStatus: 'paid',
                purchaseDate: '2024-01-10',
                dueDate: '2024-02-10',
                items: [
                    { productName: 'لابتوب HP', quantity: 5, price: 2500 },
                    { productName: 'ماوس لاسلكي', quantity: 10, price: 100 }
                ]
            },
            {
                id: 2002,
                supplierName: 'مؤسسة الإلكترونيات الحديثة',
                supplierPhone: '0113456789',
                totalAmount: 8500,
                paidAmount: 5000,
                remainingAmount: 3500,
                paymentStatus: 'partial',
                purchaseDate: '2024-01-15',
                dueDate: '2024-02-15',
                items: [
                    { productName: 'هاتف Samsung', quantity: 3, price: 2800 }
                ]
            },
            {
                id: 2003,
                supplierName: 'شركة الأجهزة المكتبية',
                supplierPhone: '0114567890',
                totalAmount: 3600,
                paidAmount: 0,
                remainingAmount: 3600,
                paymentStatus: 'unpaid',
                purchaseDate: '2024-01-20',
                dueDate: '2024-02-20',
                items: [
                    { productName: 'طابعة Canon', quantity: 3, price: 1200 }
                ]
            }
        ];

        // تحميل المشتريات
        async function loadPurchases() {
            await crudManager.read('purchases', {
                mockData: mockPurchases,
                onSuccess: (data) => {
                    updateStats(data);
                    console.log('تم تحميل المشتريات بنجاح:', data.length);
                },
                onError: (error) => {
                    console.error('خطأ في تحميل المشتريات:', error);
                }
            });
        }

        // تحميل إحصائيات المشتريات
        function loadPurchasesStats() {
            // حساب الإحصائيات من البيانات المحملة
            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            let todayTotal = 0;
            let monthTotal = 0;
            let yearTotal = 0;
            let grandTotal = 0;

            purchases.forEach(purchase => {
                const purchaseDate = new Date(purchase.purchaseDate);
                const amount = purchase.totalAmount;

                grandTotal += amount;

                if (purchase.purchaseDate === today) {
                    todayTotal += amount;
                }

                if (purchaseDate.getMonth() === currentMonth && purchaseDate.getFullYear() === currentYear) {
                    monthTotal += amount;
                }

                if (purchaseDate.getFullYear() === currentYear) {
                    yearTotal += amount;
                }
            });

            document.getElementById('todayPurchases').textContent = todayTotal.toLocaleString() + ' ر.س';
            document.getElementById('monthPurchases').textContent = monthTotal.toLocaleString() + ' ر.س';
            document.getElementById('yearPurchases').textContent = yearTotal.toLocaleString() + ' ر.س';
            document.getElementById('totalPurchases').textContent = grandTotal.toLocaleString() + ' ر.س';
        }

        // عرض المشتريات
        function displayPurchases(purchases) {
            const tbody = document.getElementById('purchasesTableBody');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const purchasesContent = document.getElementById('purchasesContent');
            const emptyState = document.getElementById('emptyState');

            if (!purchases || purchases.length === 0) {
                loadingIndicator.style.display = 'none';
                purchasesContent.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tbody.innerHTML = '';
            purchases.forEach(purchase => {
                const statusText = {
                    'completed': 'مكتملة',
                    'pending': 'معلقة',
                    'cancelled': 'ملغية'
                };

                const statusClass = {
                    'completed': 'status-completed',
                    'pending': 'status-pending',
                    'cancelled': 'status-cancelled'
                };

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: 600; color: #667eea;">${purchase.invoiceNumber}</div>
                    </td>
                    <td>${purchase.supplierName}</td>
                    <td>${new Date(purchase.purchaseDate).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <div style="font-weight: 600; color: #2c3e50;">${purchase.totalAmount.toLocaleString()} ر.س</div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: #27ae60;">${purchase.paidAmount.toLocaleString()} ر.س</div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: ${purchase.remainingAmount > 0 ? '#e74c3c' : '#27ae60'};">
                            ${purchase.remainingAmount.toLocaleString()} ر.س
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${statusClass[purchase.status]}">
                            ${statusText[purchase.status]}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-info btn-sm" onclick="viewPurchase(${purchase.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="editPurchase(${purchase.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deletePurchase(${purchase.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('purchasesContent').style.display = 'block';
            
            updatePagination();
        }

        // تحديث التصفح
        function updatePagination() {
            totalPages = Math.ceil(purchases.length / itemsPerPage);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                const button = document.createElement('button');
                button.textContent = i;
                button.className = i === currentPage ? 'active' : '';
                button.onclick = () => {
                    currentPage = i;
                    displayPurchases();
                };
                pagination.appendChild(button);
            }
        }

        // البحث في المشتريات
        function searchPurchases() {
            // تطبيق فلاتر البحث
            console.log('البحث في المشتريات...');
        }

        // عرض تفاصيل المشتريات
        function viewPurchase(id) {
            window.location.href = `purchase-details.html?id=${id}`;
        }

        // تعديل المشتريات
        function editPurchase(id) {
            window.location.href = `edit-purchase.html?id=${id}`;
        }

        // حذف المشتريات
        function deletePurchase(id) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                console.log('حذف الفاتورة:', id);
            }
        }

        // البحث في المشتريات
        function searchPurchases() {
            const filters = {
                supplierName: document.getElementById('searchSupplier').value,
                paymentStatus: document.getElementById('searchStatus').value
            };

            // Remove empty filters
            Object.keys(filters).forEach(key => {
                if (!filters[key]) delete filters[key];
            });

            crudManager.search(filters);
        }

        // تصدير المشتريات
        function exportPurchases() {
            crudManager.showNotification('جاري تحضير ملف التصدير...', 'info');
            setTimeout(() => {
                crudManager.showNotification('تم تصدير بيانات المشتريات بنجاح', 'success');
            }, 2000);
        }

        // عرض تفاصيل المشتريات
        function viewPurchase(id) {
            const purchase = crudManager.data.find(p => p.id == id);
            if (purchase) {
                const itemsList = purchase.items.map(item =>
                    `- ${item.productName}: ${item.quantity} × ${item.price.toLocaleString()} ر.س`
                ).join('\n');

                alert(`تفاصيل فاتورة المشتريات:\nالرقم: PUR-${id.toString().padStart(4, '0')}\nالمورد: ${purchase.supplierName}\nالهاتف: ${purchase.supplierPhone}\nالإجمالي: ${purchase.totalAmount.toLocaleString()} ر.س\nالمدفوع: ${purchase.paidAmount.toLocaleString()} ر.س\nالمتبقي: ${purchase.remainingAmount.toLocaleString()} ر.س\nالحالة: ${getStatusText(purchase.paymentStatus)}\n\nالأصناف:\n${itemsList}`);
            }
        }

        // تعديل المشتريات
        function editPurchase(id) {
            crudManager.showNotification('ميزة التعديل قيد التطوير وستكون متاحة قريباً', 'info');
        }

        // حذف المشتريات
        async function deletePurchase(id) {
            try {
                await crudManager.delete('purchases', id, {
                    confirmMessage: 'هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.',
                    onSuccess: () => {
                        loadPurchases();
                    }
                });
            } catch (error) {
                console.error('خطأ في حذف الفاتورة:', error);
            }
        }

        // تحديث الإحصائيات
        function updateStats(purchases) {
            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            let todayTotal = 0;
            let monthTotal = 0;
            let yearTotal = 0;
            let grandTotal = 0;

            purchases.forEach(purchase => {
                const purchaseDate = new Date(purchase.purchaseDate);
                const amount = purchase.totalAmount;

                grandTotal += amount;

                if (purchase.purchaseDate === today) {
                    todayTotal += amount;
                }

                if (purchaseDate.getMonth() === currentMonth && purchaseDate.getFullYear() === currentYear) {
                    monthTotal += amount;
                }

                if (purchaseDate.getFullYear() === currentYear) {
                    yearTotal += amount;
                }
            });

            document.getElementById('todayPurchases').textContent = todayTotal.toLocaleString() + ' ر.س';
            document.getElementById('monthPurchases').textContent = monthTotal.toLocaleString() + ' ر.س';
            document.getElementById('yearPurchases').textContent = yearTotal.toLocaleString() + ' ر.س';
            document.getElementById('totalPurchases').textContent = grandTotal.toLocaleString() + ' ر.س';
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadPurchases();

            // Add search event listeners
            const searchSupplier = document.getElementById('searchSupplier');
            const searchStatus = document.getElementById('searchStatus');

            if (searchSupplier) searchSupplier.addEventListener('input', searchPurchases);
            if (searchStatus) searchStatus.addEventListener('change', searchPurchases);
        });
    </script>
</body>
</html>
