const express = require('express');
const { body, param, query } = require('express-validator');
const userController = require('../controllers/userController');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع الطرق
router.use(authMiddleware.authenticate);

// قواعد التحقق من صحة البيانات
const createUserValidation = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('اسم المستخدم يجب أن يكون بين 3 و 50 حرف')
    .isAlphanumeric()
    .withMessage('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط'),
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 8 })
    .withMessage('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص'),
  body('firstName')
    .isLength({ min: 2, max: 50 })
    .withMessage('الاسم الأول يجب أن يكون بين 2 و 50 حرف')
    .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
    .withMessage('الاسم الأول يجب أن يحتوي على أحرف عربية أو إنجليزية فقط'),
  body('lastName')
    .isLength({ min: 2, max: 50 })
    .withMessage('الاسم الأخير يجب أن يكون بين 2 و 50 حرف')
    .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
    .withMessage('الاسم الأخير يجب أن يحتوي على أحرف عربية أو إنجليزية فقط'),
  body('phone')
    .optional()
    .matches(/^[\+]?[0-9\-\(\)\s]+$/)
    .withMessage('رقم الهاتف غير صحيح'),
  body('roleId')
    .isInt({ min: 1 })
    .withMessage('معرف الدور يجب أن يكون رقم صحيح'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('حالة النشاط يجب أن تكون true أو false'),
  body('emailVerified')
    .optional()
    .isBoolean()
    .withMessage('حالة تأكيد البريد يجب أن تكون true أو false')
];

const updateUserValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('معرف المستخدم يجب أن يكون رقم صحيح'),
  body('username')
    .optional()
    .isLength({ min: 3, max: 50 })
    .withMessage('اسم المستخدم يجب أن يكون بين 3 و 50 حرف')
    .isAlphanumeric()
    .withMessage('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail(),
  body('firstName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('الاسم الأول يجب أن يكون بين 2 و 50 حرف'),
  body('lastName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('الاسم الأخير يجب أن يكون بين 2 و 50 حرف'),
  body('phone')
    .optional()
    .matches(/^[\+]?[0-9\-\(\)\s]+$/)
    .withMessage('رقم الهاتف غير صحيح'),
  body('roleId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('معرف الدور يجب أن يكون رقم صحيح'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('حالة النشاط يجب أن تكون true أو false'),
  body('emailVerified')
    .optional()
    .isBoolean()
    .withMessage('حالة تأكيد البريد يجب أن تكون true أو false')
];

const getUserValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('معرف المستخدم يجب أن يكون رقم صحيح')
];

const searchValidation = [
  query('q')
    .isLength({ min: 2 })
    .withMessage('مصطلح البحث يجب أن يكون حرفين على الأقل'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50')
];

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         username:
 *           type: string
 *         email:
 *           type: string
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         fullName:
 *           type: string
 *         phone:
 *           type: string
 *         avatar:
 *           type: string
 *         isActive:
 *           type: boolean
 *         emailVerified:
 *           type: boolean
 *         lastLogin:
 *           type: string
 *           format: date-time
 *         role:
 *           type: object
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: الحصول على جميع المستخدمين
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: رقم الصفحة
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: عدد العناصر في الصفحة
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: مصطلح البحث
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *         description: فلترة حسب الدور
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [all, active, inactive]
 *         description: فلترة حسب الحالة
 *     responses:
 *       200:
 *         description: قائمة المستخدمين
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 pagination:
 *                   type: object
 */
router.get('/', 
  authMiddleware.authorize(['users.view']), 
  userController.getUsers
);

/**
 * @swagger
 * /api/users/search:
 *   get:
 *     summary: البحث في المستخدمين
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: مصطلح البحث
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: حد النتائج
 *     responses:
 *       200:
 *         description: نتائج البحث
 */
router.get('/search', 
  authMiddleware.authorize(['users.view']), 
  searchValidation,
  userController.searchUsers
);

/**
 * @swagger
 * /api/users/stats:
 *   get:
 *     summary: الحصول على إحصائيات المستخدمين
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: إحصائيات المستخدمين
 */
router.get('/stats', 
  authMiddleware.authorize(['users.view']), 
  userController.getUserStats
);

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: الحصول على مستخدم واحد
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: معرف المستخدم
 *     responses:
 *       200:
 *         description: بيانات المستخدم
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       404:
 *         description: المستخدم غير موجود
 */
router.get('/:id', 
  authMiddleware.authorize(['users.view']), 
  getUserValidation,
  userController.getUser
);

/**
 * @swagger
 * /api/users:
 *   post:
 *     summary: إنشاء مستخدم جديد
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *               - roleId
 *             properties:
 *               username:
 *                 type: string
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *               roleId:
 *                 type: integer
 *               isActive:
 *                 type: boolean
 *               emailVerified:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: تم إنشاء المستخدم بنجاح
 *       400:
 *         description: بيانات غير صحيحة
 */
router.post('/', 
  authMiddleware.authorize(['users.create']), 
  createUserValidation,
  userController.createUser
);

/**
 * @swagger
 * /api/users/{id}:
 *   put:
 *     summary: تحديث مستخدم
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: معرف المستخدم
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *               email:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *               roleId:
 *                 type: integer
 *               isActive:
 *                 type: boolean
 *               emailVerified:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: تم تحديث المستخدم بنجاح
 *       404:
 *         description: المستخدم غير موجود
 */
router.put('/:id', 
  authMiddleware.authorize(['users.edit']), 
  updateUserValidation,
  userController.updateUser
);

/**
 * @swagger
 * /api/users/{id}:
 *   delete:
 *     summary: حذف مستخدم
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: معرف المستخدم
 *     responses:
 *       200:
 *         description: تم حذف المستخدم بنجاح
 *       404:
 *         description: المستخدم غير موجود
 */
router.delete('/:id', 
  authMiddleware.authorize(['users.delete']), 
  getUserValidation,
  userController.deleteUser
);

/**
 * @swagger
 * /api/users/{id}/unlock:
 *   post:
 *     summary: إلغاء قفل المستخدم
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: معرف المستخدم
 *     responses:
 *       200:
 *         description: تم إلغاء قفل المستخدم بنجاح
 *       404:
 *         description: المستخدم غير موجود
 */
router.post('/:id/unlock', 
  authMiddleware.authorize(['users.edit']), 
  getUserValidation,
  userController.unlockUser
);

module.exports = router;
