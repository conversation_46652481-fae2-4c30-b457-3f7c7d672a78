<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الوكالة التجارية المتكامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
            text-align: right;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            direction: rtl;
        }
        
        input:focus {
            outline: none;
            border-color: #2196F3;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
            margin-top: 1rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .server-status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
        }
        
        .server-online {
            background: #d4edda;
            color: #155724;
        }
        
        .server-offline {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">و</div>
        <h1>نظام الوكالة التجارية المتكامل</h1>
        <p class="subtitle">نظام إدارة شامل للأعمال التجارية</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="Admin@123456" required>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">تسجيل الدخول</button>
        </form>
        
        <div id="status" class="status" style="display: none;"></div>
        <div id="serverStatus" class="server-status">جاري فحص الخادم...</div>
    </div>

    <script>
        const API_URL = 'http://localhost:3001';
        
        // فحص حالة الخادم
        async function checkServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            try {
                const response = await fetch(`${API_URL}/health`);
                if (response.ok) {
                    statusDiv.textContent = '✅ الخادم متصل';
                    statusDiv.className = 'server-status server-online';
                } else {
                    throw new Error('Server error');
                }
            } catch (error) {
                statusDiv.textContent = '❌ الخادم غير متصل';
                statusDiv.className = 'server-status server-offline';
            }
        }
        
        // تسجيل الدخول
        async function login(username, password) {
            const response = await fetch(`${API_URL}/api/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });
            
            const data = await response.json();
            return { response, data };
        }
        
        // معالج النموذج
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const statusDiv = document.getElementById('status');
            const loginBtn = document.getElementById('loginBtn');
            
            // إظهار حالة التحميل
            statusDiv.style.display = 'block';
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'جاري تسجيل الدخول...';
            loginBtn.disabled = true;
            
            try {
                const { response, data } = await login(username, password);
                
                if (response.ok && data.success) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `مرحباً ${data.data.user.fullName}! تم تسجيل الدخول بنجاح`;
                    
                    // حفظ التوكن
                    localStorage.setItem('accessToken', data.data.accessToken);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    
                    setTimeout(() => {
                        statusDiv.textContent += '\n🎉 النظام جاهز للاستخدام!';
                        // إعادة توجيه إلى لوحة التحكم
                        setTimeout(() => {
                            window.location.href = '/dashboard.html';
                        }, 2000);
                    }, 1000);
                    
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = data.message || 'فشل في تسجيل الدخول';
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = 'خطأ في الاتصال بالخادم';
            } finally {
                loginBtn.disabled = false;
            }
        });
        
        // فحص حالة الخادم عند تحميل الصفحة
        checkServerStatus();
        
        // فحص دوري لحالة الخادم
        setInterval(checkServerStatus, 10000);
    </script>
</body>
</html>
