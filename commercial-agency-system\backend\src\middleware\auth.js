const { verifyToken } = require('../config/auth');
const { User, Role } = require('../models');

// وسطاء المصادقة
const authenticate = async (req, res, next) => {
  try {
    // الحصول على الرمز المميز من الهيدر
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.error('الرمز المميز مطلوب', 401);
    }
    
    const token = authHeader.substring(7); // إزالة "Bearer "
    
    if (!token) {
      return res.error('الرمز المميز مطلوب', 401);
    }
    
    // التحقق من صحة الرمز المميز
    const decoded = verifyToken(token);
    
    // البحث عن المستخدم
    const user = await User.findByPk(decoded.id, {
      include: [{
        model: Role,
        as: 'role',
        attributes: ['id', 'name', 'name_ar', 'permissions']
      }]
    });
    
    if (!user || !user.is_active) {
      return res.error('المستخدم غير موجود أو غير نشط', 401);
    }
    
    // التحقق من قفل الحساب
    if (user.isLocked()) {
      return res.error('الحساب مقفل', 423);
    }
    
    // إضافة معلومات المستخدم إلى الطلب
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      fullName: user.getFullName(),
      role: {
        id: user.role.id,
        name: user.role.name,
        nameAr: user.role.name_ar,
        permissions: user.role.permissions || []
      }
    };
    
    next();
    
  } catch (error) {
    console.error('خطأ في المصادقة:', error.message);
    
    if (error.message.includes('انتهت صلاحية')) {
      return res.error('انتهت صلاحية الرمز المميز', 401);
    } else if (error.message.includes('غير صحيح')) {
      return res.error('الرمز المميز غير صحيح', 401);
    } else {
      return res.error('فشل في المصادقة', 401);
    }
  }
};

// وسطاء التحقق من الصلاحيات
const authorize = (requiredPermissions = []) => {
  return (req, res, next) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        return res.error('المصادقة مطلوبة', 401);
      }
      
      // إذا لم تكن هناك صلاحيات مطلوبة، السماح بالمرور
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return next();
      }
      
      const userPermissions = req.user.role.permissions || [];
      
      // التحقق من صلاحية المدير الكامل
      if (userPermissions.includes('admin.all')) {
        return next();
      }
      
      // التحقق من الصلاحيات المطلوبة
      const hasPermission = requiredPermissions.some(permission => 
        userPermissions.includes(permission)
      );
      
      if (!hasPermission) {
        return res.error('ليس لديك صلاحية للوصول إلى هذا المورد', 403);
      }
      
      next();
      
    } catch (error) {
      console.error('خطأ في التحقق من الصلاحيات:', error.message);
      res.error('حدث خطأ في التحقق من الصلاحيات', 500);
    }
  };
};

// وسطاء التحقق من الدور
const requireRole = (requiredRoles = []) => {
  return (req, res, next) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        return res.error('المصادقة مطلوبة', 401);
      }
      
      // إذا لم تكن هناك أدوار مطلوبة، السماح بالمرور
      if (!requiredRoles || requiredRoles.length === 0) {
        return next();
      }
      
      const userRole = req.user.role.name;
      
      // التحقق من دور المدير
      if (userRole === 'admin') {
        return next();
      }
      
      // التحقق من الأدوار المطلوبة
      const hasRole = requiredRoles.includes(userRole);
      
      if (!hasRole) {
        return res.error('ليس لديك الدور المطلوب للوصول إلى هذا المورد', 403);
      }
      
      next();
      
    } catch (error) {
      console.error('خطأ في التحقق من الدور:', error.message);
      res.error('حدث خطأ في التحقق من الدور', 500);
    }
  };
};

// وسطاء اختياري للمصادقة (لا يرفض الطلب إذا لم يكن هناك رمز مميز)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }
    
    const token = authHeader.substring(7);
    
    if (!token) {
      return next();
    }
    
    try {
      const decoded = verifyToken(token);
      
      const user = await User.findByPk(decoded.id, {
        include: [{
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'name_ar', 'permissions']
        }]
      });
      
      if (user && user.is_active && !user.isLocked()) {
        req.user = {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          fullName: user.getFullName(),
          role: {
            id: user.role.id,
            name: user.role.name,
            nameAr: user.role.name_ar,
            permissions: user.role.permissions || []
          }
        };
      }
    } catch (error) {
      // تجاهل أخطاء الرمز المميز في المصادقة الاختيارية
    }
    
    next();
    
  } catch (error) {
    console.error('خطأ في المصادقة الاختيارية:', error.message);
    next();
  }
};

// وسطاء التحقق من ملكية المورد
const checkOwnership = (resourceModel, resourceIdParam = 'id', ownerField = 'created_by') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params[resourceIdParam];
      const userId = req.user.id;
      const userRole = req.user.role.name;
      
      // المدير يمكنه الوصول إلى جميع الموارد
      if (userRole === 'admin') {
        return next();
      }
      
      // البحث عن المورد
      const resource = await resourceModel.findByPk(resourceId);
      
      if (!resource) {
        return res.error('المورد غير موجود', 404);
      }
      
      // التحقق من الملكية
      if (resource[ownerField] !== userId) {
        return res.error('ليس لديك صلاحية للوصول إلى هذا المورد', 403);
      }
      
      next();
      
    } catch (error) {
      console.error('خطأ في التحقق من الملكية:', error.message);
      res.error('حدث خطأ في التحقق من الملكية', 500);
    }
  };
};

// وسطاء تسجيل العمليات
const logActivity = (action, resourceType) => {
  return (req, res, next) => {
    // حفظ معلومات العملية للتسجيل لاحقاً
    req.activityLog = {
      action,
      resourceType,
      userId: req.user ? req.user.id : null,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    };
    
    next();
  };
};

// وسطاء التحقق من حد الطلبات للمستخدم
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();
  
  return (req, res, next) => {
    if (!req.user) {
      return next();
    }
    
    const userId = req.user.id;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // الحصول على طلبات المستخدم
    let requests = userRequests.get(userId) || [];
    
    // تنظيف الطلبات القديمة
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // التحقق من تجاوز الحد
    if (requests.length >= maxRequests) {
      return res.error('تم تجاوز الحد المسموح من الطلبات', 429);
    }
    
    // إضافة الطلب الحالي
    requests.push(now);
    userRequests.set(userId, requests);
    
    next();
  };
};

module.exports = {
  authenticate,
  authorize,
  requireRole,
  optionalAuth,
  checkOwnership,
  logActivity,
  userRateLimit
};
