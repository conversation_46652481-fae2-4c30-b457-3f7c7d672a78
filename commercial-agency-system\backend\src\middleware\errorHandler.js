const { ValidationError, UniqueConstraintError, ForeignKeyConstraintError } = require('sequelize');

// وسطاء معالجة الأخطاء العامة
const errorHandler = (err, req, res, next) => {
  console.error('خطأ في التطبيق:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    user: req.user ? req.user.id : 'غير مصادق عليه',
    timestamp: new Date().toISOString()
  });

  // أخطاء Sequelize
  if (err instanceof ValidationError) {
    const errors = err.errors.map(error => ({
      field: error.path,
      message: getArabicValidationMessage(error.validatorKey, error.path),
      value: error.value
    }));
    
    return res.error('بيانات غير صحيحة', 400, errors);
  }

  if (err instanceof UniqueConstraintError) {
    const field = err.errors[0].path;
    const fieldName = getArabicFieldName(field);
    return res.error(`${fieldName} مستخدم بالفعل`, 400);
  }

  if (err instanceof ForeignKeyConstraintError) {
    return res.error('لا يمكن تنفيذ العملية بسبب وجود بيانات مرتبطة', 400);
  }

  // أخطاء JWT
  if (err.name === 'JsonWebTokenError') {
    return res.error('الرمز المميز غير صحيح', 401);
  }

  if (err.name === 'TokenExpiredError') {
    return res.error('انتهت صلاحية الرمز المميز', 401);
  }

  // أخطاء التحقق من صحة البيانات
  if (err.name === 'ValidationError' && err.details) {
    const errors = err.details.map(detail => ({
      field: detail.path[0],
      message: detail.message
    }));
    return res.error('بيانات غير صحيحة', 400, errors);
  }

  // أخطاء Multer (رفع الملفات)
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.error('حجم الملف كبير جداً', 400);
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    return res.error('عدد الملفات كبير جداً', 400);
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.error('نوع الملف غير مدعوم', 400);
  }

  // أخطاء قاعدة البيانات
  if (err.name === 'SequelizeConnectionError') {
    return res.error('خطأ في الاتصال بقاعدة البيانات', 500);
  }

  if (err.name === 'SequelizeTimeoutError') {
    return res.error('انتهت مهلة الاتصال بقاعدة البيانات', 500);
  }

  // أخطاء HTTP
  if (err.status || err.statusCode) {
    const statusCode = err.status || err.statusCode;
    const message = err.message || getHttpErrorMessage(statusCode);
    return res.error(message, statusCode);
  }

  // أخطاء مخصصة
  if (err.isCustomError) {
    return res.error(err.message, err.statusCode || 400);
  }

  // خطأ عام
  const isDevelopment = process.env.NODE_ENV === 'development';
  const message = isDevelopment ? err.message : 'حدث خطأ داخلي في الخادم';
  
  res.error(message, 500, isDevelopment ? { stack: err.stack } : null);
};

// وسطاء معالجة الطلبات غير الموجودة
const notFoundHandler = (req, res, next) => {
  res.error(`المسار غير موجود: ${req.method} ${req.url}`, 404);
};

// وسطاء التحقق من صحة JSON
const jsonErrorHandler = (err, req, res, next) => {
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.error('تنسيق JSON غير صحيح', 400);
  }
  next(err);
};

// وسطاء معالجة أخطاء async
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// دالة للحصول على رسالة خطأ HTTP بالعربية
const getHttpErrorMessage = (statusCode) => {
  const messages = {
    400: 'طلب غير صحيح',
    401: 'غير مصرح له',
    403: 'ممنوع',
    404: 'غير موجود',
    405: 'الطريقة غير مسموحة',
    406: 'غير مقبول',
    408: 'انتهت مهلة الطلب',
    409: 'تعارض',
    410: 'لم يعد متاحاً',
    413: 'الحمولة كبيرة جداً',
    415: 'نوع الوسائط غير مدعوم',
    422: 'كيان غير قابل للمعالجة',
    429: 'طلبات كثيرة جداً',
    500: 'خطأ داخلي في الخادم',
    501: 'غير مطبق',
    502: 'بوابة سيئة',
    503: 'الخدمة غير متاحة',
    504: 'انتهت مهلة البوابة'
  };
  
  return messages[statusCode] || 'خطأ غير معروف';
};

// دالة للحصول على رسالة التحقق بالعربية
const getArabicValidationMessage = (validatorKey, field) => {
  const fieldName = getArabicFieldName(field);
  
  const messages = {
    notNull: `${fieldName} مطلوب`,
    notEmpty: `${fieldName} لا يمكن أن يكون فارغاً`,
    isEmail: `${fieldName} يجب أن يكون بريد إلكتروني صحيح`,
    isUrl: `${fieldName} يجب أن يكون رابط صحيح`,
    isInt: `${fieldName} يجب أن يكون رقم صحيح`,
    isFloat: `${fieldName} يجب أن يكون رقم عشري`,
    isDecimal: `${fieldName} يجب أن يكون رقم عشري`,
    isAlpha: `${fieldName} يجب أن يحتوي على أحرف فقط`,
    isAlphanumeric: `${fieldName} يجب أن يحتوي على أحرف وأرقام فقط`,
    isNumeric: `${fieldName} يجب أن يحتوي على أرقام فقط`,
    isDate: `${fieldName} يجب أن يكون تاريخ صحيح`,
    isBefore: `${fieldName} يجب أن يكون قبل التاريخ المحدد`,
    isAfter: `${fieldName} يجب أن يكون بعد التاريخ المحدد`,
    max: `${fieldName} كبير جداً`,
    min: `${fieldName} صغير جداً`,
    len: `${fieldName} طوله غير صحيح`,
    is: `${fieldName} تنسيقه غير صحيح`
  };
  
  return messages[validatorKey] || `${fieldName} غير صحيح`;
};

// دالة للحصول على اسم الحقل بالعربية
const getArabicFieldName = (field) => {
  const fieldNames = {
    // حقول المستخدم
    username: 'اسم المستخدم',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    password_hash: 'كلمة المرور',
    first_name: 'الاسم الأول',
    last_name: 'الاسم الأخير',
    phone: 'رقم الهاتف',
    mobile: 'رقم الجوال',
    avatar: 'الصورة الشخصية',
    role_id: 'الدور',
    
    // حقول العميل
    customer_code: 'رمز العميل',
    customer_type: 'نوع العميل',
    company_name: 'اسم الشركة',
    tax_number: 'الرقم الضريبي',
    commercial_register: 'السجل التجاري',
    national_id: 'رقم الهوية',
    address: 'العنوان',
    city: 'المدينة',
    country: 'البلد',
    postal_code: 'الرمز البريدي',
    customer_category: 'فئة العميل',
    credit_limit: 'حد الائتمان',
    payment_terms: 'شروط الدفع',
    discount_percentage: 'نسبة الخصم',
    
    // حقول المورد
    supplier_code: 'رمز المورد',
    contact_person: 'الشخص المسؤول',
    fax: 'الفاكس',
    website: 'الموقع الإلكتروني',
    supplier_type: 'نوع المورد',
    rating: 'التقييم',
    
    // حقول المنتج
    product_code: 'رمز المنتج',
    barcode: 'الباركود',
    name: 'الاسم',
    name_ar: 'الاسم بالعربية',
    description: 'الوصف',
    category_id: 'الفئة',
    brand_id: 'العلامة التجارية',
    unit_id: 'وحدة القياس',
    purchase_price: 'سعر الشراء',
    selling_price: 'سعر البيع',
    min_selling_price: 'أقل سعر بيع',
    wholesale_price: 'سعر الجملة',
    cost_price: 'سعر التكلفة',
    weight: 'الوزن',
    dimensions: 'الأبعاد',
    color: 'اللون',
    size: 'الحجم',
    warranty_period: 'فترة الضمان',
    reorder_level: 'حد إعادة الطلب',
    max_stock_level: 'الحد الأقصى للمخزون',
    tax_rate: 'معدل الضريبة',
    
    // حقول عامة
    notes: 'الملاحظات',
    is_active: 'الحالة',
    created_at: 'تاريخ الإنشاء',
    updated_at: 'تاريخ التحديث',
    created_by: 'أنشئ بواسطة'
  };
  
  return fieldNames[field] || field;
};

// فئة خطأ مخصصة
class CustomError extends Error {
  constructor(message, statusCode = 400) {
    super(message);
    this.name = 'CustomError';
    this.statusCode = statusCode;
    this.isCustomError = true;
    Error.captureStackTrace(this, this.constructor);
  }
}

// دالة لإنشاء خطأ مخصص
const createError = (message, statusCode = 400) => {
  return new CustomError(message, statusCode);
};

module.exports = {
  errorHandler,
  notFoundHandler,
  jsonErrorHandler,
  asyncHandler,
  CustomError,
  createError
};
