import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// تعريف أنواع البيانات
export interface CompanySettings {
  name: string;
  nameEn: string;
  logo?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  taxNumber: string;
  commercialRegister: string;
}

export interface SystemSettings {
  defaultCurrency: string;
  defaultTaxRate: number;
  defaultLanguage: 'ar' | 'en';
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  fiscalYearStart: string;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  sessionTimeout: number;
}

export interface NotificationSettings {
  email: {
    enabled: boolean;
    lowStock: boolean;
    newOrders: boolean;
    paymentDue: boolean;
    systemUpdates: boolean;
  };
  push: {
    enabled: boolean;
    lowStock: boolean;
    newOrders: boolean;
    paymentDue: boolean;
  };
  sms: {
    enabled: boolean;
    criticalAlerts: boolean;
  };
}

export interface SecuritySettings {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    expirationDays: number;
  };
  twoFactorAuth: {
    enabled: boolean;
    method: 'sms' | 'email' | 'app';
  };
  sessionSettings: {
    maxConcurrentSessions: number;
    idleTimeout: number;
    rememberMeDuration: number;
  };
  auditLog: {
    enabled: boolean;
    retentionDays: number;
  };
}

export interface PrintSettings {
  defaultPrinter: string;
  paperSize: 'A4' | 'A3' | 'Letter';
  orientation: 'portrait' | 'landscape';
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  includeHeader: boolean;
  includeFooter: boolean;
  includeWatermark: boolean;
  watermarkText: string;
}

export interface IntegrationSettings {
  paymentGateway: {
    enabled: boolean;
    provider: string;
    apiKey: string;
    testMode: boolean;
  };
  shipping: {
    enabled: boolean;
    provider: string;
    apiKey: string;
  };
  accounting: {
    enabled: boolean;
    provider: string;
    syncFrequency: 'realtime' | 'hourly' | 'daily';
  };
}

export interface SettingsState {
  company: CompanySettings;
  system: SystemSettings;
  notifications: NotificationSettings;
  security: SecuritySettings;
  print: PrintSettings;
  integrations: IntegrationSettings;
  theme: {
    mode: 'light' | 'dark' | 'auto';
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: 'small' | 'medium' | 'large';
  };
  layout: {
    sidebarWidth: number;
    headerHeight: number;
    footerHeight: number;
    borderRadius: number;
    spacing: number;
  };
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// الحالة الأولية
const initialState: SettingsState = {
  company: {
    name: 'الوكالة التجارية المتكاملة',
    nameEn: 'Integrated Commercial Agency',
    address: 'الرياض، المملكة العربية السعودية',
    phone: '+966-11-1234567',
    email: '<EMAIL>',
    website: 'https://commercial-agency.com',
    taxNumber: '123456789012345',
    commercialRegister: '1010123456',
  },
  system: {
    defaultCurrency: 'SAR',
    defaultTaxRate: 15,
    defaultLanguage: 'ar',
    timezone: 'Asia/Riyadh',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'HH:mm',
    fiscalYearStart: '01/01',
    backupFrequency: 'daily',
    sessionTimeout: 30,
  },
  notifications: {
    email: {
      enabled: true,
      lowStock: true,
      newOrders: true,
      paymentDue: true,
      systemUpdates: false,
    },
    push: {
      enabled: true,
      lowStock: true,
      newOrders: true,
      paymentDue: true,
    },
    sms: {
      enabled: false,
      criticalAlerts: false,
    },
  },
  security: {
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      expirationDays: 90,
    },
    twoFactorAuth: {
      enabled: false,
      method: 'email',
    },
    sessionSettings: {
      maxConcurrentSessions: 3,
      idleTimeout: 30,
      rememberMeDuration: 30,
    },
    auditLog: {
      enabled: true,
      retentionDays: 365,
    },
  },
  print: {
    defaultPrinter: '',
    paperSize: 'A4',
    orientation: 'portrait',
    margins: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20,
    },
    includeHeader: true,
    includeFooter: true,
    includeWatermark: false,
    watermarkText: '',
  },
  integrations: {
    paymentGateway: {
      enabled: false,
      provider: '',
      apiKey: '',
      testMode: true,
    },
    shipping: {
      enabled: false,
      provider: '',
      apiKey: '',
    },
    accounting: {
      enabled: false,
      provider: '',
      syncFrequency: 'daily',
    },
  },
  theme: {
    mode: 'light',
    primaryColor: '#2196f3',
    secondaryColor: '#f50057',
    fontFamily: 'Cairo',
    fontSize: 'medium',
  },
  layout: {
    sidebarWidth: 280,
    headerHeight: 64,
    footerHeight: 48,
    borderRadius: 8,
    spacing: 8,
  },
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// إنشاء الـ slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // تحديث إعدادات الشركة
    updateCompanySettings: (state, action: PayloadAction<Partial<CompanySettings>>) => {
      state.company = { ...state.company, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث إعدادات النظام
    updateSystemSettings: (state, action: PayloadAction<Partial<SystemSettings>>) => {
      state.system = { ...state.system, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث إعدادات الإشعارات
    updateNotificationSettings: (state, action: PayloadAction<Partial<NotificationSettings>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث إعدادات الأمان
    updateSecuritySettings: (state, action: PayloadAction<Partial<SecuritySettings>>) => {
      state.security = { ...state.security, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث إعدادات الطباعة
    updatePrintSettings: (state, action: PayloadAction<Partial<PrintSettings>>) => {
      state.print = { ...state.print, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث إعدادات التكامل
    updateIntegrationSettings: (state, action: PayloadAction<Partial<IntegrationSettings>>) => {
      state.integrations = { ...state.integrations, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث إعدادات الثيم
    updateThemeSettings: (state, action: PayloadAction<Partial<SettingsState['theme']>>) => {
      state.theme = { ...state.theme, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث إعدادات التخطيط
    updateLayoutSettings: (state, action: PayloadAction<Partial<SettingsState['layout']>>) => {
      state.layout = { ...state.layout, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // تعيين حالة التحميل
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    // تعيين الخطأ
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // مسح الخطأ
    clearError: (state) => {
      state.error = null;
    },
    
    // إعادة تعيين الإعدادات للافتراضية
    resetSettings: (state) => {
      return { ...initialState };
    },
    
    // تحديث جميع الإعدادات
    updateAllSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      return { ...state, ...action.payload, lastUpdated: new Date().toISOString() };
    },
    
    // تبديل وضع الثيم
    toggleThemeMode: (state) => {
      state.theme.mode = state.theme.mode === 'light' ? 'dark' : 'light';
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث اللغة
    updateLanguage: (state, action: PayloadAction<'ar' | 'en'>) => {
      state.system.defaultLanguage = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث العملة
    updateCurrency: (state, action: PayloadAction<string>) => {
      state.system.defaultCurrency = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
    
    // تحديث معدل الضريبة
    updateTaxRate: (state, action: PayloadAction<number>) => {
      state.system.defaultTaxRate = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
  },
});

// تصدير الإجراءات
export const {
  updateCompanySettings,
  updateSystemSettings,
  updateNotificationSettings,
  updateSecuritySettings,
  updatePrintSettings,
  updateIntegrationSettings,
  updateThemeSettings,
  updateLayoutSettings,
  setLoading,
  setError,
  clearError,
  resetSettings,
  updateAllSettings,
  toggleThemeMode,
  updateLanguage,
  updateCurrency,
  updateTaxRate,
} = settingsSlice.actions;

// Selectors
export const selectSettings = (state: { settings: SettingsState }) => state.settings;
export const selectCompanySettings = (state: { settings: SettingsState }) => state.settings.company;
export const selectSystemSettings = (state: { settings: SettingsState }) => state.settings.system;
export const selectNotificationSettings = (state: { settings: SettingsState }) => state.settings.notifications;
export const selectSecuritySettings = (state: { settings: SettingsState }) => state.settings.security;
export const selectPrintSettings = (state: { settings: SettingsState }) => state.settings.print;
export const selectIntegrationSettings = (state: { settings: SettingsState }) => state.settings.integrations;
export const selectThemeSettings = (state: { settings: SettingsState }) => state.settings.theme;
export const selectLayoutSettings = (state: { settings: SettingsState }) => state.settings.layout;

// تصدير المقلل
export default settingsSlice.reducer;
