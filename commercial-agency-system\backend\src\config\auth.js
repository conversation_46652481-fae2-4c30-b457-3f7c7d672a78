const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');

dotenv.config();

// إعدادات JWT
const JWT_CONFIG = {
  secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
  refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
  expiresIn: process.env.JWT_EXPIRE || '24h',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRE || '7d',
  issuer: 'commercial-agency-system',
  audience: 'commercial-agency-users'
};

// إعدادات التشفير
const BCRYPT_CONFIG = {
  rounds: parseInt(process.env.BCRYPT_ROUNDS) || 12
};

// إنشاء JWT Token
const generateToken = (payload) => {
  try {
    const token = jwt.sign(
      {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
        type: 'access'
      },
      JWT_CONFIG.secret,
      {
        expiresIn: JWT_CONFIG.expiresIn,
        issuer: JWT_CONFIG.issuer,
        audience: JWT_CONFIG.audience
      }
    );
    return token;
  } catch (error) {
    console.error('خطأ في إنشاء الرمز المميز:', error.message);
    throw new Error('فشل في إنشاء الرمز المميز');
  }
};

// إنشاء Refresh Token
const generateRefreshToken = (payload) => {
  try {
    const refreshToken = jwt.sign(
      {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
        type: 'refresh'
      },
      JWT_CONFIG.refreshSecret,
      {
        expiresIn: JWT_CONFIG.refreshExpiresIn,
        issuer: JWT_CONFIG.issuer,
        audience: JWT_CONFIG.audience
      }
    );
    return refreshToken;
  } catch (error) {
    console.error('خطأ في إنشاء رمز التحديث:', error.message);
    throw new Error('فشل في إنشاء رمز التحديث');
  }
};

// التحقق من صحة JWT Token
const verifyToken = (token) => {
  try {
    const decoded = jwt.verify(token, JWT_CONFIG.secret, {
      issuer: JWT_CONFIG.issuer,
      audience: JWT_CONFIG.audience
    });
    
    if (decoded.type !== 'access') {
      throw new Error('نوع الرمز المميز غير صحيح');
    }
    
    return decoded;
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('انتهت صلاحية الرمز المميز');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('الرمز المميز غير صحيح');
    } else {
      throw new Error('فشل في التحقق من الرمز المميز');
    }
  }
};

// التحقق من صحة Refresh Token
const verifyRefreshToken = (refreshToken) => {
  try {
    const decoded = jwt.verify(refreshToken, JWT_CONFIG.refreshSecret, {
      issuer: JWT_CONFIG.issuer,
      audience: JWT_CONFIG.audience
    });
    
    if (decoded.type !== 'refresh') {
      throw new Error('نوع رمز التحديث غير صحيح');
    }
    
    return decoded;
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('انتهت صلاحية رمز التحديث');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('رمز التحديث غير صحيح');
    } else {
      throw new Error('فشل في التحقق من رمز التحديث');
    }
  }
};

// تشفير كلمة المرور
const hashPassword = async (password) => {
  try {
    const salt = await bcrypt.genSalt(BCRYPT_CONFIG.rounds);
    const hashedPassword = await bcrypt.hash(password, salt);
    return hashedPassword;
  } catch (error) {
    console.error('خطأ في تشفير كلمة المرور:', error.message);
    throw new Error('فشل في تشفير كلمة المرور');
  }
};

// مقارنة كلمة المرور
const comparePassword = async (password, hashedPassword) => {
  try {
    const isMatch = await bcrypt.compare(password, hashedPassword);
    return isMatch;
  } catch (error) {
    console.error('خطأ في مقارنة كلمة المرور:', error.message);
    throw new Error('فشل في التحقق من كلمة المرور');
  }
};

// إنشاء كلمة مرور عشوائية
const generateRandomPassword = (length = 12) => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  
  return password;
};

// التحقق من قوة كلمة المرور
const validatePasswordStrength = (password) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const errors = [];
  
  if (password.length < minLength) {
    errors.push(`كلمة المرور يجب أن تكون ${minLength} أحرف على الأقل`);
  }
  
  if (!hasUpperCase) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  }
  
  if (!hasLowerCase) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  }
  
  if (!hasNumbers) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  }
  
  if (!hasSpecialChar) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    strength: calculatePasswordStrength(password)
  };
};

// حساب قوة كلمة المرور
const calculatePasswordStrength = (password) => {
  let score = 0;
  
  // الطول
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  
  // التنوع
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;
  
  // التعقيد
  if (password.length >= 16) score += 1;
  if (/(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^A-Za-z0-9])/.test(password)) score += 1;
  
  if (score < 3) return 'ضعيف';
  if (score < 5) return 'متوسط';
  if (score < 7) return 'قوي';
  return 'قوي جداً';
};

// إنشاء رمز التحقق
const generateVerificationCode = (length = 6) => {
  const digits = '0123456789';
  let code = '';
  
  for (let i = 0; i < length; i++) {
    code += digits.charAt(Math.floor(Math.random() * digits.length));
  }
  
  return code;
};

// إنشاء رمز إعادة تعيين كلمة المرور
const generateResetToken = () => {
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
};

// التحقق من انتهاء صلاحية الرمز المميز
const isTokenExpired = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
};

// استخراج معلومات المستخدم من الرمز المميز
const extractUserFromToken = (token) => {
  try {
    const decoded = verifyToken(token);
    return {
      id: decoded.id,
      username: decoded.username,
      email: decoded.email,
      role: decoded.role,
      permissions: decoded.permissions
    };
  } catch (error) {
    throw error;
  }
};

// إعدادات الجلسة
const SESSION_CONFIG = {
  secret: process.env.SESSION_SECRET || 'your-session-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 ساعة
  }
};

module.exports = {
  JWT_CONFIG,
  BCRYPT_CONFIG,
  SESSION_CONFIG,
  generateToken,
  generateRefreshToken,
  verifyToken,
  verifyRefreshToken,
  hashPassword,
  comparePassword,
  generateRandomPassword,
  validatePasswordStrength,
  calculatePasswordStrength,
  generateVerificationCode,
  generateResetToken,
  isTokenExpired,
  extractUserFromToken
};
