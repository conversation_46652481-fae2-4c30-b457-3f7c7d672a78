const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// نموذج الأدوار
const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [2, 50],
      notEmpty: true
    }
  },
  name_ar: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'name_ar',
    validate: {
      len: [2, 50],
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'updated_at'
  }
}, {
  tableName: 'roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['name']
    },
    {
      fields: ['is_active']
    }
  ]
});

// الصلاحيات المتاحة في النظام
const AVAILABLE_PERMISSIONS = {
  // إدارة المستخدمين
  'users.view': 'عرض المستخدمين',
  'users.create': 'إضافة مستخدمين',
  'users.edit': 'تعديل المستخدمين',
  'users.delete': 'حذف المستخدمين',
  'users.manage_roles': 'إدارة أدوار المستخدمين',
  
  // إدارة العملاء
  'customers.view': 'عرض العملاء',
  'customers.create': 'إضافة عملاء',
  'customers.edit': 'تعديل العملاء',
  'customers.delete': 'حذف العملاء',
  'customers.view_balance': 'عرض أرصدة العملاء',
  
  // إدارة الموردين
  'suppliers.view': 'عرض الموردين',
  'suppliers.create': 'إضافة موردين',
  'suppliers.edit': 'تعديل الموردين',
  'suppliers.delete': 'حذف الموردين',
  'suppliers.view_balance': 'عرض أرصدة الموردين',
  
  // إدارة المنتجات
  'products.view': 'عرض المنتجات',
  'products.create': 'إضافة منتجات',
  'products.edit': 'تعديل المنتجات',
  'products.delete': 'حذف المنتجات',
  'products.manage_categories': 'إدارة فئات المنتجات',
  'products.manage_brands': 'إدارة العلامات التجارية',
  
  // إدارة المخزون
  'inventory.view': 'عرض المخزون',
  'inventory.adjust': 'تعديل المخزون',
  'inventory.transfer': 'نقل المخزون',
  'inventory.view_movements': 'عرض حركات المخزون',
  
  // إدارة المشتريات
  'purchases.view': 'عرض المشتريات',
  'purchases.create': 'إنشاء طلبات شراء',
  'purchases.edit': 'تعديل طلبات الشراء',
  'purchases.approve': 'اعتماد طلبات الشراء',
  'purchases.receive': 'استلام المشتريات',
  'purchases.create_invoice': 'إنشاء فواتير شراء',
  
  // إدارة المبيعات
  'sales.view': 'عرض المبيعات',
  'sales.create_quotation': 'إنشاء عروض أسعار',
  'sales.create_invoice': 'إنشاء فواتير بيع',
  'sales.edit': 'تعديل المبيعات',
  'sales.approve_discount': 'اعتماد الخصومات',
  
  // إدارة المدفوعات
  'payments.view': 'عرض المدفوعات',
  'payments.create': 'إنشاء مدفوعات',
  'payments.edit': 'تعديل المدفوعات',
  'payments.approve': 'اعتماد المدفوعات',
  
  // التقارير
  'reports.sales': 'تقارير المبيعات',
  'reports.purchases': 'تقارير المشتريات',
  'reports.inventory': 'تقارير المخزون',
  'reports.financial': 'التقارير المالية',
  'reports.customers': 'تقارير العملاء',
  'reports.suppliers': 'تقارير الموردين',
  
  // إعدادات النظام
  'settings.view': 'عرض الإعدادات',
  'settings.edit': 'تعديل الإعدادات',
  'settings.backup': 'النسخ الاحتياطي',
  'settings.audit_logs': 'عرض سجل العمليات',
  
  // صلاحيات خاصة
  'admin.all': 'جميع الصلاحيات'
};

// الأدوار الافتراضية
const DEFAULT_ROLES = {
  admin: {
    name: 'admin',
    name_ar: 'مدير النظام',
    description: 'مدير النظام - صلاحيات كاملة',
    permissions: ['admin.all']
  },
  sales_manager: {
    name: 'sales_manager',
    name_ar: 'مدير المبيعات',
    description: 'مدير المبيعات - إدارة العملاء والمبيعات',
    permissions: [
      'customers.view', 'customers.create', 'customers.edit', 'customers.view_balance',
      'sales.view', 'sales.create_quotation', 'sales.create_invoice', 'sales.edit',
      'products.view', 'inventory.view',
      'reports.sales', 'reports.customers'
    ]
  },
  purchase_manager: {
    name: 'purchase_manager',
    name_ar: 'مدير المشتريات',
    description: 'مدير المشتريات - إدارة الموردين والمشتريات',
    permissions: [
      'suppliers.view', 'suppliers.create', 'suppliers.edit', 'suppliers.view_balance',
      'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.approve', 'purchases.receive',
      'products.view', 'products.create', 'products.edit',
      'inventory.view', 'inventory.adjust',
      'reports.purchases', 'reports.suppliers', 'reports.inventory'
    ]
  },
  accountant: {
    name: 'accountant',
    name_ar: 'محاسب',
    description: 'المحاسب - إدارة المالية والمديونية',
    permissions: [
      'customers.view', 'customers.view_balance',
      'suppliers.view', 'suppliers.view_balance',
      'payments.view', 'payments.create', 'payments.edit', 'payments.approve',
      'reports.financial', 'reports.sales', 'reports.purchases'
    ]
  },
  sales_employee: {
    name: 'sales_employee',
    name_ar: 'موظف مبيعات',
    description: 'موظف المبيعات - عمليات البيع فقط',
    permissions: [
      'customers.view', 'customers.create', 'customers.edit',
      'sales.view', 'sales.create_quotation', 'sales.create_invoice',
      'products.view', 'inventory.view'
    ]
  },
  warehouse_keeper: {
    name: 'warehouse_keeper',
    name_ar: 'أمين المخزن',
    description: 'أمين المخزن - إدارة المخزون',
    permissions: [
      'products.view', 'products.create', 'products.edit',
      'inventory.view', 'inventory.adjust', 'inventory.transfer', 'inventory.view_movements',
      'purchases.receive',
      'reports.inventory'
    ]
  }
};

// الدوال الإضافية للنموذج
Role.prototype.hasPermission = function(permission) {
  if (!this.permissions || !Array.isArray(this.permissions)) {
    return false;
  }
  
  // إذا كان لديه صلاحية admin.all
  if (this.permissions.includes('admin.all')) {
    return true;
  }
  
  return this.permissions.includes(permission);
};

Role.prototype.addPermission = function(permission) {
  if (!this.permissions) {
    this.permissions = [];
  }
  
  if (!this.permissions.includes(permission)) {
    this.permissions.push(permission);
  }
};

Role.prototype.removePermission = function(permission) {
  if (!this.permissions) {
    return;
  }
  
  this.permissions = this.permissions.filter(p => p !== permission);
};

Role.prototype.getPermissionsList = function() {
  if (!this.permissions || !Array.isArray(this.permissions)) {
    return [];
  }
  
  return this.permissions.map(permission => ({
    key: permission,
    name: AVAILABLE_PERMISSIONS[permission] || permission
  }));
};

// الدوال الثابتة
Role.findByName = async function(name) {
  return await this.findOne({
    where: { name, is_active: true }
  });
};

Role.findActiveRoles = async function(options = {}) {
  return await this.findAll({
    where: { is_active: true },
    ...options
  });
};

Role.createRole = async function(roleData) {
  try {
    const role = await this.create(roleData);
    return role;
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      throw new Error('اسم الدور مستخدم بالفعل');
    }
    throw error;
  }
};

Role.updateRole = async function(id, roleData) {
  const role = await this.findByPk(id);
  if (!role) {
    throw new Error('الدور غير موجود');
  }
  
  await role.update(roleData);
  return role;
};

Role.deleteRole = async function(id) {
  const role = await this.findByPk(id);
  if (!role) {
    throw new Error('الدور غير موجود');
  }
  
  // التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
  const User = require('./User');
  const usersCount = await User.count({ where: { role_id: id, is_active: true } });
  
  if (usersCount > 0) {
    throw new Error('لا يمكن حذف الدور لوجود مستخدمين مرتبطين به');
  }
  
  // حذف منطقي
  await role.update({ is_active: false });
  return role;
};

Role.getAvailablePermissions = function() {
  return AVAILABLE_PERMISSIONS;
};

Role.getDefaultRoles = function() {
  return DEFAULT_ROLES;
};

Role.seedDefaultRoles = async function() {
  try {
    for (const [key, roleData] of Object.entries(DEFAULT_ROLES)) {
      const existingRole = await this.findByName(roleData.name);
      if (!existingRole) {
        await this.create(roleData);
        console.log(`✅ تم إنشاء الدور: ${roleData.name_ar}`);
      }
    }
  } catch (error) {
    console.error('❌ خطأ في إنشاء الأدوار الافتراضية:', error.message);
    throw error;
  }
};

// تصدير النموذج
module.exports = Role;
