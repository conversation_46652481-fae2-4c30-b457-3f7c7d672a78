import createCache from '@emotion/cache';
import { prefixer } from 'stylis';
import rtlPlugin from 'stylis-plugin-rtl';

const isBrowser = typeof document !== 'undefined';

// على الخادم، إنشاء meta tag جديد لكل طلب لتجنب تضارب الأنماط
// في العميل، استخدام meta tag واحد مع id ثابت
export default function createEmotionCache() {
  let insertionPoint;

  if (isBrowser) {
    const emotionInsertionPoint = document.querySelector<HTMLMetaElement>(
      'meta[name="emotion-insertion-point"]',
    );
    insertionPoint = emotionInsertionPoint ?? undefined;
  }

  return createCache({
    key: 'mui-style-rtl',
    stylisPlugins: [prefixer, rtlPlugin],
    insertionPoint,
  });
}
