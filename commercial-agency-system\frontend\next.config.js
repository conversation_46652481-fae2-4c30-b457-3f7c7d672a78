/** @type {import('next').NextConfig} */

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig = {
  // إعدادات React
  reactStrictMode: true,
  swcMinify: true,

  // إعدادات i18n للدعم متعدد اللغات
  i18n: {
    locales: ['ar', 'en'],
    defaultLocale: 'ar',
    localeDetection: true,
  },

  // إعدادات الصور
  images: {
    domains: [
      'localhost',
      'commercial-agency.com',
      'api.commercial-agency.com'
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // إعدادات البيئة
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
    API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:5000/api',
    APP_NAME: 'نظام الوكالة التجارية المتكامل',
    APP_VERSION: '1.0.0',
  },

  // إعدادات الأمان
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },

  // إعدادات إعادة التوجيه
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
      },
    ];
  },

  // إعدادات إعادة الكتابة
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.API_BASE_URL || 'http://localhost:5000/api'}/:path*`,
      },
    ];
  },

  // إعدادات Webpack
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // إضافة alias للمسارات
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
      '@/components': require('path').resolve(__dirname, 'src/components'),
      '@/pages': require('path').resolve(__dirname, 'src/pages'),
      '@/hooks': require('path').resolve(__dirname, 'src/hooks'),
      '@/utils': require('path').resolve(__dirname, 'src/utils'),
      '@/store': require('path').resolve(__dirname, 'src/store'),
      '@/types': require('path').resolve(__dirname, 'src/types'),
      '@/styles': require('path').resolve(__dirname, 'src/styles'),
    };

    // تحسينات الإنتاج
    if (!dev && !isServer) {
      config.optimization.splitChunks.chunks = 'all';
      config.optimization.splitChunks.cacheGroups = {
        ...config.optimization.splitChunks.cacheGroups,
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        mui: {
          test: /[\\/]node_modules[\\/]@mui[\\/]/,
          name: 'mui',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      };
    }

    // إضافة دعم للملفات
    config.module.rules.push({
      test: /\.(pdf|doc|docx|xls|xlsx)$/,
      use: {
        loader: 'file-loader',
        options: {
          publicPath: '/_next/static/files/',
          outputPath: 'static/files/',
        },
      },
    });

    return config;
  },

  // إعدادات التجميع
  experimental: {
    // تفعيل ميزات تجريبية
    appDir: false,
    serverComponentsExternalPackages: ['@mui/material'],
  },

  // إعدادات الأداء
  compress: true,
  poweredByHeader: false,
  generateEtags: true,

  // إعدادات TypeScript
  typescript: {
    ignoreBuildErrors: false,
  },

  // إعدادات ESLint
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['src'],
  },

  // إعدادات الصفحات
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // إعدادات الخادم
  serverRuntimeConfig: {
    // متغيرات الخادم فقط
    mySecret: 'secret',
  },

  publicRuntimeConfig: {
    // متغيرات عامة للعميل والخادم
    staticFolder: '/static',
    apiUrl: process.env.API_BASE_URL || 'http://localhost:5000/api',
  },

  // إعدادات التصدير
  trailingSlash: false,
  exportPathMap: async function (
    defaultPathMap,
    { dev, dir, outDir, distDir, buildId }
  ) {
    return {
      '/': { page: '/' },
      '/dashboard': { page: '/dashboard' },
      '/login': { page: '/auth/login' },
    };
  },

  // إعدادات PWA (إذا كان مطلوباً)
  // pwa: {
  //   dest: 'public',
  //   register: true,
  //   skipWaiting: true,
  // },
};

module.exports = withBundleAnalyzer(nextConfig);
