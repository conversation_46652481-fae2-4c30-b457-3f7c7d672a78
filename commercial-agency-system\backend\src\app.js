const express = require('express');
const dotenv = require('dotenv');
const path = require('path');

// تحميل متغيرات البيئة
dotenv.config();

// استيراد الإعدادات والوسطاء
const { createApp, SERVER_CONFIG } = require('./config/app');
const { initializeDatabase } = require('./config/database');
const { errorHandler, notFoundHandler, jsonErrorHandler } = require('./middleware/errorHandler');

// استيراد النماذج
const { syncDatabase, seedDatabase, validateDatabase } = require('./models');

// استيراد الطرق
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
// const customerRoutes = require('./routes/customers');
// const supplierRoutes = require('./routes/suppliers');
// const productRoutes = require('./routes/products');
// const inventoryRoutes = require('./routes/inventory');
// const purchaseRoutes = require('./routes/purchases');
// const salesRoutes = require('./routes/sales');
// const paymentRoutes = require('./routes/payments');
// const reportRoutes = require('./routes/reports');

// إنشاء التطبيق
const app = createApp();

// وسطاء معالجة أخطاء JSON
app.use(jsonErrorHandler);

// إعداد الطرق
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
// app.use('/api/customers', customerRoutes);
// app.use('/api/suppliers', supplierRoutes);
// app.use('/api/products', productRoutes);
// app.use('/api/inventory', inventoryRoutes);
// app.use('/api/purchases', purchaseRoutes);
// app.use('/api/sales', salesRoutes);
// app.use('/api/payments', paymentRoutes);
// app.use('/api/reports', reportRoutes);

// طريق الصحة
app.get('/health', (req, res) => {
  res.success({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
    database: 'connected' // سيتم تحديثه بعد فحص قاعدة البيانات
  }, 'الخادم يعمل بشكل طبيعي');
});

// طريق معلومات النظام
app.get('/api/system/info', (req, res) => {
  res.success({
    name: 'نظام الوكالة التجارية المتكامل',
    nameEn: 'Integrated Commercial Agency System',
    version: '1.0.0',
    description: 'نظام إدارة شامل للوكالات التجارية يشمل إدارة العملاء والموردين والمخزون والمبيعات والمشتريات',
    author: 'Commercial Agency System Team',
    license: 'MIT',
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
    platform: process.platform,
    architecture: process.arch,
    timezone: process.env.TIMEZONE || 'Asia/Riyadh',
    defaultCurrency: process.env.DEFAULT_CURRENCY || 'SAR',
    features: [
      'إدارة المستخدمين والصلاحيات',
      'إدارة العملاء والموردين',
      'إدارة المنتجات والمخزون',
      'عمليات البيع والشراء',
      'إدارة المدفوعات والمديونية',
      'التقارير والتحليلات',
      'النسخ الاحتياطي والأمان'
    ]
  }, 'معلومات النظام');
});

// طريق إحصائيات النظام
app.get('/api/system/stats', async (req, res) => {
  try {
    const { getDatabaseStats } = require('./models');
    const stats = await getDatabaseStats();
    
    res.success({
      database: stats,
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        platform: process.platform,
        nodeVersion: process.version
      },
      timestamp: new Date().toISOString()
    }, 'إحصائيات النظام');
    
  } catch (error) {
    console.error('خطأ في جلب إحصائيات النظام:', error);
    res.error('حدث خطأ في جلب إحصائيات النظام', 500);
  }
});

// إعداد Swagger للتوثيق (في بيئة التطوير فقط)
if (process.env.NODE_ENV === 'development' && process.env.SWAGGER_ENABLED === 'true') {
  const swaggerJsdoc = require('swagger-jsdoc');
  const swaggerUi = require('swagger-ui-express');
  
  const swaggerOptions = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Commercial Agency System API',
        version: '1.0.0',
        description: 'نظام الوكالة التجارية المتكامل - واجهة برمجة التطبيقات',
        contact: {
          name: 'فريق التطوير',
          email: '<EMAIL>'
        }
      },
      servers: [
        {
          url: `http://localhost:${SERVER_CONFIG.port}`,
          description: 'خادم التطوير'
        }
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      }
    },
    apis: ['./src/routes/*.js']
  };
  
  const swaggerSpec = swaggerJsdoc(swaggerOptions);
  
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Commercial Agency API Docs'
  }));
  
  // طريق للحصول على مواصفات Swagger بصيغة JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
}

// وسطاء معالجة الطلبات غير الموجودة
app.use(notFoundHandler);

// وسطاء معالجة الأخطاء (يجب أن يكون الأخير)
app.use(errorHandler);

// دالة بدء الخادم
const startServer = async () => {
  try {
    console.log('🚀 بدء تشغيل نظام الوكالة التجارية...');
    
    // تهيئة قاعدة البيانات
    console.log('📊 تهيئة قاعدة البيانات...');
    await initializeDatabase();
    
    // مزامنة النماذج
    console.log('🔄 مزامنة نماذج قاعدة البيانات...');
    await syncDatabase();
    
    // إنشاء البيانات الأساسية
    console.log('🌱 إنشاء البيانات الأساسية...');
    await seedDatabase();
    
    // التحقق من صحة قاعدة البيانات
    console.log('🔍 التحقق من صحة قاعدة البيانات...');
    const isValid = await validateDatabase();
    
    if (!isValid) {
      throw new Error('قاعدة البيانات غير صحيحة');
    }
    
    // بدء الخادم
    const server = app.listen(SERVER_CONFIG.port, SERVER_CONFIG.host, () => {
      console.log('🎉 تم تشغيل الخادم بنجاح!');
      console.log(`🌐 الخادم يعمل على: http://${SERVER_CONFIG.host}:${SERVER_CONFIG.port}`);
      console.log(`📝 البيئة: ${SERVER_CONFIG.environment}`);
      
      if (process.env.SWAGGER_ENABLED === 'true') {
        console.log(`📚 توثيق API: http://${SERVER_CONFIG.host}:${SERVER_CONFIG.port}/api-docs`);
      }
      
      console.log('✅ النظام جاهز للاستخدام');
    });
    
    // معالجة إغلاق الخادم بشكل صحيح
    const gracefulShutdown = async (signal) => {
      console.log(`\n📴 تم استلام إشارة ${signal}، بدء إغلاق الخادم...`);
      
      server.close(async () => {
        console.log('🔌 تم إغلاق الخادم');
        
        try {
          const { closeConnection } = require('./config/database');
          await closeConnection();
          console.log('📊 تم إغلاق اتصال قاعدة البيانات');
        } catch (error) {
          console.error('❌ خطأ في إغلاق قاعدة البيانات:', error.message);
        }
        
        console.log('👋 تم إغلاق النظام بنجاح');
        process.exit(0);
      });
      
      // إجبار الإغلاق بعد 10 ثوان
      setTimeout(() => {
        console.error('⚠️  إجبار إغلاق النظام');
        process.exit(1);
      }, 10000);
    };
    
    // الاستماع لإشارات الإغلاق
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // معالجة الأخطاء غير المعالجة
    process.on('uncaughtException', (error) => {
      console.error('❌ خطأ غير معالج:', error);
      process.exit(1);
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ رفض غير معالج في:', promise, 'السبب:', reason);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ فشل في تشغيل الخادم:', error.message);
    process.exit(1);
  }
};

// بدء الخادم إذا تم تشغيل الملف مباشرة
if (require.main === module) {
  startServer();
}

module.exports = app;
